{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<center><h1>第一章 预备知识</h1></center>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 一、Python基础\n", "### 1. 列表推导式与条件赋值\n", "\n", "在生成一个数字序列的时候，在`Python`中可以如下写出："]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["[0, 2, 4, 6, 8]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["L = []\n", "def my_func(x):\n", "    return 2*x\n", "for i in range(5):\n", "    L.append(my_func(i))\n", "L"]}, {"cell_type": "markdown", "metadata": {}, "source": ["事实上可以利用列表推导式进行写法上的简化：`[* for i in *]`。其中，第一个`*`为映射函数，其输入为后面`i`指代的内容，第二个`*`表示迭代的对象。"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["[0, 2, 4, 6, 8]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["[my_func(i) for i in range(5)]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["列表表达式还支持多层嵌套，如下面的例子中第一个`for`为外层循环，第二个为内层循环："]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["['a_c', 'a_d', 'b_c', 'b_d', 'o_c', 'o_d']"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["[m+'_'+n for m in ['a', 'b','o'] for n in ['c', 'd']]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["除了列表推导式，另一个实用的语法糖是带有`if`选择的条件赋值，其形式为`value = a if condition else b`："]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"ename": "SyntaxError", "evalue": "expected 'else' after 'if' expression (3410203459.py, line 1)", "output_type": "error", "traceback": ["\u001b[1;36m  Cell \u001b[1;32mIn[5], line 1\u001b[1;36m\u001b[0m\n\u001b[1;33m    value = 'cat' if 2>1 #else 'dog'\u001b[0m\n\u001b[1;37m            ^\u001b[0m\n\u001b[1;31mSyntaxError\u001b[0m\u001b[1;31m:\u001b[0m expected 'else' after 'if' expression\n"]}], "source": ["value = 'cat' if 2>1 else 'dog'\n", "value"]}, {"cell_type": "markdown", "metadata": {}, "source": ["等价于如下的写法：\n", "```python\n", "a, b = 'cat', 'dog'\n", "condition = 2 > 1 # 此时为True\n", "if condition:\n", "    value = a\n", "else:\n", "    value = b\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["下面举一个例子，截断列表中超过5的元素，即超过5的用5代替，小于5的保留原来的值："]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["[1, 2, 3, 4, 5, 5, 5]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["L = [1, 2, 3, 4, 5, 6, 7]\n", "[i if i <= 5 else 5 for i in L]"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["[0, 1, 2, 3, 4, 'over', 'over']"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["L=range(7)\n", "[i if i<=4 else \"over\" for i in L]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2. 匿名函数与map方法 \n", "\n", "有一些函数的定义具有清晰简单的映射关系，例如上面的`my_func`函数，这时候可以用匿名函数的方法简洁地表示： 形成个人习惯，f_custom= lambda x,y:x+y"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["6"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["my_func = lambda x: 2*x\n", "my_func(3)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["3"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["multi_para_func = lambda a, b: a + b\n", "multi_para_func(1, 2) "]}, {"cell_type": "markdown", "metadata": {}, "source": ["但上面的用法其实违背了“匿名”的含义，事实上它往往在无需多处调用的场合进行使用，例如上面列表推导式中的例子，用户不关心函数的名字，只关心这种映射的关系："]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["[0, 2, 4, 6, 8]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["[(lambda x: 2*x)(i) for i in range(5)]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["注意，函数用括号包起来"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["[2, 3, 0, 1, 6]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["[(lambda x:x^2)(i) for i in range(5)]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["对于上述的这种列表推导式的匿名函数映射，`Python`中提供了`map`函数来完成，它返回的是一个`map`对象，需要通过`list`转为列表： 这种设计是map返回的是迭代器，当需要的时候"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["<map at 0x20fb900abf0>"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["map(lambda x: 2*x, range(5))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["对于多个输入值的函数映射，可以通过追加迭代对象实现："]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["['0_a', '1_b', '2_c', '3_d', '4_e']"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["list(map(lambda x, y: str(x)+'_'+y, range(5), list('abcde')))"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["['0_a',\n", " '0_b',\n", " '0_c',\n", " '0_d',\n", " '0_e',\n", " '1_a',\n", " '1_b',\n", " '1_c',\n", " '1_d',\n", " '1_e',\n", " '2_a',\n", " '2_b',\n", " '2_c',\n", " '2_d',\n", " '2_e',\n", " '3_a',\n", " '3_b',\n", " '3_c',\n", " '3_d',\n", " '3_e',\n", " '4_a',\n", " '4_b',\n", " '4_c',\n", " '4_d',\n", " '4_e']"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["[str(x)+\"_\"+ y for x in range(5) for y in \"abcde\"]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3. zip对象与enumerate方法\n", "\n", "`zip`函数能够把多个可迭代对象打包成一个元组构成的可迭代对象，它返回了一个`zip`对象，通过`tuple`, `list`可以得到相应的打包结果："]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["[('a', 'd', 'h'), ('b', 'e', 'i'), ('c', 'f', 'j'), ('d', 'd', '4')]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["L1, L2, L3 = list('abcd'), list('defd'), list('hij4')\n", "list(zip(L1, L2, L3))"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["(('a', 'd', 'h'), ('b', 'e', 'i'), ('c', 'f', 'j'))"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["tuple(zip(L1, L2, L3))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["往往会在循环迭代的时候使用到`zip`函数："]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["a d h\n", "b e i\n", "c f j\n"]}], "source": ["for i, j, k in zip(L1, L2, L3):\n", "     print(i, j, k)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["`enumerate`是一种特殊的打包，它可以在迭代时绑定迭代元素的遍历序号："]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0 a\n", "1 b\n", "2 c\n", "3 d\n"]}], "source": ["L = list('abcd')\n", "for index, value in enumerate(L):\n", "     print(index, value)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["用`zip`对象也能够简单地实现这个功能："]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0 a\n", "1 b\n", "2 c\n", "3 d\n"]}], "source": ["for index, value in zip(range(len(L)), L):\n", "     print(index, value)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["当需要对两个列表建立字典映射时，可以利用`zip`对象："]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["{0: 'a',\n", " 1: 'b',\n", " 2: 'c',\n", " 3: 'd',\n", " 4: 'e',\n", " 5: 'f',\n", " 6: 'g',\n", " 7: 'h',\n", " 8: 'i',\n", " 9: 'j'}"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["dict(zip(range(10),list(\"abcdefghij\")))"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'a': 'd', 'b': 'e', 'c': 'f'}"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["dict(zip(L1, L2))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["既然有了压缩函数，那么`Python`也提供了`*`操作符和`zip`联合使用来进行解压操作： * 是函数解包的用法，相当于mathematica 的Sequence【】"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/plain": ["[('a', 'd', 'h'), ('b', 'e', 'i'), ('c', 'f', 'j')]"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["zipped = list(zip(L1, L2, L3))\n", "zipped"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["[('a', 'b', 'c'), ('d', 'e', 'f'), ('h', 'i', 'j')]"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["list(zip(*zipped)) # 三个元组分别对应原来的列表"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 二、Numpy基础\n", "### 1. np数组的构造\n", "最一般的方法是通过`array`来构造："]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([1, 2, 3])"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import numpy as np\n", "np.array([1,2,3])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["下面讨论一些特殊数组的生成方式：\n", "\n", "【a】等差序列：`np.linspace`, `np.arange`"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([1. , 1.4, 1.8, 2.2, 2.6, 3. , 3.4, 3.8, 4.2, 4.6, 5. ])"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["np.linspace(1,5,11) # 起始、终止（包含）、样本个数"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([1, 3])"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["np.arange(1,5,2) # 起始、终止（不包含）、步长"]}, {"cell_type": "markdown", "metadata": {}, "source": ["【b】特殊矩阵：`zeros`, `eye`, `full`"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[0., 0., 0.],\n", "       [0., 0., 0.]])"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["np.zeros((2,3)) # 传入元组表示各维度大小"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[1., 0., 0.],\n", "       [0., 1., 0.],\n", "       [0., 0., 1.]])"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["np.eye(3) # 3*3的单位矩阵"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[0., 1., 0.],\n", "       [0., 0., 1.],\n", "       [0., 0., 0.]])"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["np.eye(3, k=1) # 偏移主对角线1个单位的伪单位矩阵"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[10, 10, 10],\n", "       [10, 10, 10]])"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["np.full((2,3), 10) # 元组传入大小，10表示填充数值"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[1, 2, 3],\n", "       [1, 2, 3]])"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["np.full((2,3), [1,2,3]) # 每行填入相同的列表"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["'yesyesyesyesyes'"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["\"\".join(list(np.full((5,5),\"yes\"))[1])"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'numpy'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[13], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[38;5;28;01<PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;21;01mnumpy\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[38;5;21;01mnp\u001b[39;00m\n\u001b[0;32m      2\u001b[0m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;241m.\u001b[39mjoin(np\u001b[38;5;241m.\u001b[39mrandom\u001b[38;5;241m.\u001b[39mchoice([\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124ma\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mt\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mc\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mg\u001b[39m\u001b[38;5;124m\"\u001b[39m],\u001b[38;5;241m10\u001b[39m))\n", "\u001b[1;31mModuleNotFoundError\u001b[0m: No module named 'numpy'"]}], "source": ["import numpy as np\n", "\"\".join(np.random.choice([\"a\",\"t\",\"c\",\"g\"],10))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["【c】随机矩阵：`np.random`\n", "\n", "最常用的随机生成函数为`rand`, `randn`, `randint`, `choice`，它们分别表示0-1均匀分布的随机数组、标准正态的随机数组、随机整数组和随机列表抽样："]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0.92340835, 0.20019461, 0.40755472])"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["np.random.rand(3) # 生成服从0-1均匀分布的三个随机数"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[0.87774012, 0.62713094, 0.52865238],\n", "       [0.67295385, 0.61559105, 0.36307302]])"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["np.random.rand(2, 3) # 注意这里传入的不是元组，每个维度大小分开输入"]}, {"cell_type": "markdown", "metadata": {}, "source": ["对于服从区间`a`到`b`上的均匀分布可以如下生成："]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([6.59370831, 8.03865138, 9.19172546])"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["a, b = 5, 15\n", "(b - a) * np.random.rand(3) + a"]}, {"cell_type": "markdown", "metadata": {}, "source": ["一般的，可以选择已有的库函数："]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([11.26499636, 13.12311185,  6.00774156])"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["np.random.uniform(5, 15, 3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["`randn`生成了`N(0,I)`的标准正态分布："]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([ 1.87000209,  1.19885561, -0.58802943])"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["np.random.randn(3)"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[-1.3642839 , -0.31497567],\n", "       [-1.9452492 , -3.17272882]])"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["np.random.randn(2, 2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["对于服从方差为$\\sigma^2$均值为$\\mu$的一元正态分布可以如下生成："]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([1.56024917, 0.22829486, 7.3764211 ])"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["sigma, mu = 2.5, 3\n", "mu + np.random.randn(3) * sigma"]}, {"cell_type": "markdown", "metadata": {}, "source": ["同样的，也可选择从已有函数生成："]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([3.53517851, 5.3441269 , 3.51192744])"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["np.random.normal(3, 2.5, 3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["`randint`可以指定生成随机整数的最小值最大值（不包含）和维度大小："]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[ 5, 12],\n", "       [14,  9]])"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["low, high, size = 5, 15, (2,2) # 生成5到14的随机整数\n", "np.random.randint(low, high, size)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["`choice`可以从给定的列表中，以一定概率和方式抽取结果，当不指定概率时为均匀采样，默认抽取方式为有放回抽样："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m运行具有“joyful-pandas (Python 3.8.20)”的单元格需要ipykernel包。\n", "\u001b[1;31m运行以下命令，将 \"ipykernel\" 安装到 Python 环境中。\n", "\u001b[1;31m命令: \"conda install -p g:\\programs\\miniconda\\envs\\joyful-pandas ipykernel --update-deps --force-reinstall\""]}], "source": ["my_list = ['a', 'b', 'c', 'd']\n", "np.random.choice(my_list, 4, replace=True\n", "                , p=[0.1, 0.7, 0.1 ,0.1])"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([['c', 'a', 'c'],\n", "       ['d', 'b', 'd'],\n", "       ['d', 'c', 'a']], dtype='<U1')"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["np.random.choice(my_list, (3,3))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["当返回的元素个数与原列表相同时，不放回抽样等价于使用`permutation`函数，即打散原列表："]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['c', 'a', 'd', 'b'], dtype='<U1')"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["np.random.permutation(my_list)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["最后，需要提到的是随机种子，它能够固定随机数的输出结果："]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.5488135039273248"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["np.random.seed(0)\n", "np.random.rand()"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.5488135039273248"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["np.random.seed(0)\n", "np.random.rand()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2. np数组的变形与合并\n", "【a】转置：`T`"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[0., 0.],\n", "       [0., 0.],\n", "       [0., 0.]])"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["np.zeros((2,3)).T"]}, {"cell_type": "markdown", "metadata": {}, "source": ["【b】合并操作：`r_`, `c_`\n", "\n", "对于二维数组而言，`r_`和`c_`分别表示上下合并和左右合并："]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[0., 0., 0.],\n", "       [0., 0., 0.],\n", "       [0., 0., 0.],\n", "       [0., 0., 0.]])"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["import numpy as np\n", "np.r_[np.zeros((2,3)),np.zeros((2,3))]"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[1 4]\n", " [2 5]\n", " [3 6]]\n"]}], "source": ["# 一维数组连接\n", "a = np.array([1, 2, 3])\n", "b = np.array([4, 5, 6])\n", "\n", "result = np.c_[a, b]\n", "print(result)\n", "# 输出:\n", "# [[1 4]\n", "#  [2 5]\n", "#  [3 6]]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["一维数组和二维数组进行合并时，应当把其视作列向量，在长度匹配的情况下只能够使用左右合并的`c_`操作："]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[0],\n", "       [0]])"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["np.array([0,0]).reshape(2,1)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"text/plain": ["ValueError('all the input arrays must have same number of dimensions, but the array at index 0 has 1 dimension(s) and the array at index 1 has 2 dimension(s)')"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["try:\n", "     np.c_[np.array([0,0]),np.zeros((2,1))]\n", "except Exception as e:\n", "     Err_Msg = e\n", "Err_Msg"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[0., 0., 0.],\n", "       [0., 0., 0.]])"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["np.c_[np.array([0,0]),np.zeros((2,2))]"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[0.        , 0.21673651, 0.48122827, 0.38521567],\n", "       [0.        , 0.53319422, 0.74695686, 0.42601438]])"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["np.c_[np.array([0,0]),np.random.rand(2,3)]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 【c】维度变换：`reshape`\n", "\n", "`reshape`能够帮助用户把原数组按照新的维度重新排列。在使用时有两种模式，分别为`C`模式和`F`模式，分别以逐行和逐列的顺序进行填充读取。"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[0, 1, 2, 3],\n", "       [4, 5, 6, 7]])"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["target = np.arange(8).reshape(2,4)\n", "target"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[0, 1],\n", "       [2, 3],\n", "       [4, 5],\n", "       [6, 7]])"]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}], "source": ["target.reshape((4,2), order='C') # 按照行读取和填充"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[0, 2],\n", "       [4, 6],\n", "       [1, 3],\n", "       [5, 7]])"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["target.reshape((4,2), order='F') # 按照列读取和填充"]}, {"cell_type": "markdown", "metadata": {}, "source": ["特别地，由于被调用数组的大小是确定的，`reshape`允许有一个维度存在空缺，此时只需填充-1即可："]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[0, 1],\n", "       [2, 3],\n", "       [4, 5],\n", "       [6, 7]])"]}, "execution_count": 50, "metadata": {}, "output_type": "execute_result"}], "source": ["target.reshape((4,-1))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["下面将`n*1`大小的数组转为1维数组的操作是经常使用的："]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[1.],\n", "       [1.],\n", "       [1.]])"]}, "execution_count": 51, "metadata": {}, "output_type": "execute_result"}], "source": ["target = np.ones((3,1))\n", "target"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([1., 1., 1.])"]}, "execution_count": 52, "metadata": {}, "output_type": "execute_result"}], "source": ["target.reshape(-1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3. np数组的切片与索引\n", "数组的切片模式支持使用`slice`类型的`start:end:step`切片，还可以直接传入列表指定某个维度的索引进行切片："]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[0, 1, 2],\n", "       [3, 4, 5],\n", "       [6, 7, 8]])"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["target = np.arange(9).reshape(3,3)\n", "target"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 一维数据的reshape 技巧"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["[array([[0.89767591, 0.58437942, 0.57124918],\n", "        [0.13199911, 0.57845996, 0.86852495],\n", "        [0.21903173, 0.6992268 , 0.94115202]]),\n", " array([[0.89767591, 0.58437942, 0.57124918, 0.13199911, 0.57845996,\n", "         0.86852495, 0.21903173, 0.6992268 , 0.94115202]]),\n", " array([[0.89767591],\n", "        [0.58437942],\n", "        [0.57124918],\n", "        [0.13199911],\n", "        [0.57845996],\n", "        [0.86852495],\n", "        [0.21903173],\n", "        [0.6992268 ],\n", "        [0.94115202]])]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import numpy as np\n", "test=np.random.rand(3,3)\n", "[test,test.reshape(-1,9),test.reshape(9,-1)]"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[0 1 2]\n", " [3 4 5]\n", " [6 7 8]]\n"]}, {"data": {"text/plain": ["array([[6, 7],\n", "       [3, 4],\n", "       [0, 1]])"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["print(target)\n", "target[::-1,:-1]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["此外，还可以利用`np.ix_`在对应的维度上使用布尔索引，但此时不能使用`slice`切片："]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[0, 2],\n", "       [6, 8]])"]}, "execution_count": 55, "metadata": {}, "output_type": "execute_result"}], "source": ["target[np.ix_([True, False, True], [True, False, True])]"]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[3, 5],\n", "       [6, 8]])"]}, "execution_count": 56, "metadata": {}, "output_type": "execute_result"}], "source": ["target[np.ix_([1,2], [True, False, True])]"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["arr = np.full((10, 10), True)\n", "\n", "arr[2,3]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["当数组维度为1维时，可以直接进行布尔索引，而无需`np.ix_`："]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[0, 1, 0],\n", "       [3, 0, 5],\n", "       [0, 7, 0]])"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["new = target.reshape(-1)\n", "new[new%2==0]\n", "\n", "target\n", "target[np.where(target%2==0)]=0\n", "target\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4. 常用函数\n", "为了简单起见，这里假设下述函数输入的数组都是一维的。\n", "\n", "#### 【a】`where`\n", "\n", "`where`是一种条件函数，可以指定满足条件与不满足条件位置对应的填充值："]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([5, 1, 5, 5])"]}, "execution_count": 58, "metadata": {}, "output_type": "execute_result"}], "source": ["a = np.array([-1,1,-1,0])\n", "np.where(a>0, a, 5) # 对应位置为True时填充a对应元素，否则填充5"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 【b】`nonzero`, `argmax`, `argmin`\n", "\n", "这三个函数返回的都是索引，`nonzero`返回非零数的索引，`argmax`, `argmin`分别返回最大和最小数的索引："]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/plain": ["(array([0, 1, 3, 4, 5], dtype=int64),)"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["a = np.array([-2,-5,0,1,3,-1])\n", "np.nonzero(a)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/plain": ["4"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["a.arg<PERSON>()"]}, {"cell_type": "code", "execution_count": 61, "metadata": {}, "outputs": [{"data": {"text/plain": ["1"]}, "execution_count": 61, "metadata": {}, "output_type": "execute_result"}], "source": ["a.arg<PERSON>()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 【c】`any`, `all`\n", "\n", "`any`指当序列至少 **存在一个** `True`或非零元素时返回`True`，否则返回`False`\n", "\n", "`all`指当序列元素 **全为** `True`或非零元素时返回`True`，否则返回`False`"]}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 62, "metadata": {}, "output_type": "execute_result"}], "source": ["a = np.array([0,1])\n", "a.any()"]}, {"cell_type": "code", "execution_count": 63, "metadata": {}, "outputs": [{"data": {"text/plain": ["False"]}, "execution_count": 63, "metadata": {}, "output_type": "execute_result"}], "source": ["a.all()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["【d】`cumprod`, `cumsum`, `diff`\n", "\n", "`cumprod`, `cumsum`分别表示累乘和累加函数，返回同长度的数组，`diff`表示和前一个元素做差，由于第一个元素为缺失值，因此在默认参数情况下，返回长度是原数组减1"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([1, 2, 6])"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["a = np.array([1,2,3])\n", "a.cum<PERSON>()"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([1, 3, 6])"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["a.cumsum()"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([1, 1])"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["np.diff(a)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["【e】 统计函数\n", "\n", "常用的统计函数包括`max, min, mean, median, std, var, sum, quantile`，其中分位数计算是全局方法，因此不能通过`array.quantile`的方法调用："]}, {"cell_type": "code", "execution_count": 67, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0, 1, 2, 3, 4])"]}, "execution_count": 67, "metadata": {}, "output_type": "execute_result"}], "source": ["target = np.arange(5)\n", "target"]}, {"cell_type": "code", "execution_count": 68, "metadata": {}, "outputs": [{"data": {"text/plain": ["4"]}, "execution_count": 68, "metadata": {}, "output_type": "execute_result"}], "source": ["target.max()"]}, {"cell_type": "code", "execution_count": 69, "metadata": {}, "outputs": [{"data": {"text/plain": ["2.0"]}, "execution_count": 69, "metadata": {}, "output_type": "execute_result"}], "source": ["np.quantile(target, 0.5) # 0.5分位数"]}, {"cell_type": "markdown", "metadata": {}, "source": ["但是对于含有缺失值的数组，它们返回的结果也是缺失值，如果需要略过缺失值，必须使用`nan*`类型的函数，上述的几个统计函数都有对应的`nan*`函数。"]}, {"cell_type": "code", "execution_count": 70, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([ 1.,  2., nan])"]}, "execution_count": 70, "metadata": {}, "output_type": "execute_result"}], "source": ["target = np.array([1, 2, np.nan])\n", "target"]}, {"cell_type": "code", "execution_count": 71, "metadata": {}, "outputs": [{"data": {"text/plain": ["nan"]}, "execution_count": 71, "metadata": {}, "output_type": "execute_result"}], "source": ["target.max()"]}, {"cell_type": "code", "execution_count": 72, "metadata": {}, "outputs": [{"data": {"text/plain": ["2.0"]}, "execution_count": 72, "metadata": {}, "output_type": "execute_result"}], "source": ["np.nanmax(target)"]}, {"cell_type": "code", "execution_count": 73, "metadata": {}, "outputs": [{"data": {"text/plain": ["1.5"]}, "execution_count": 73, "metadata": {}, "output_type": "execute_result"}], "source": ["np.nanquantile(target, 0.5)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["对于协方差和相关系数分别可以利用`cov, corrcoef`如下计算："]}, {"cell_type": "code", "execution_count": 74, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[ 11.66666667, -16.66666667],\n", "       [-16.66666667,  38.66666667]])"]}, "execution_count": 74, "metadata": {}, "output_type": "execute_result"}], "source": ["target1 = np.array([1,3,5,9])\n", "target2 = np.array([1,5,3,-9])\n", "np.cov(target1, target2)"]}, {"cell_type": "code", "execution_count": 75, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[ 1.        , -0.78470603],\n", "       [-0.78470603,  1.        ]])"]}, "execution_count": 75, "metadata": {}, "output_type": "execute_result"}], "source": ["np.corr<PERSON><PERSON>(target1, target2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["最后，需要说明二维`Numpy`数组中统计函数的`axis`参数，它能够进行某一个维度下的统计特征计算，当`axis=0`时结果为列的统计指标，当`axis=1`时结果为行的统计指标："]}, {"cell_type": "code", "execution_count": 76, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[1, 2, 3],\n", "       [4, 5, 6],\n", "       [7, 8, 9]])"]}, "execution_count": 76, "metadata": {}, "output_type": "execute_result"}], "source": ["target = np.arange(1,10).reshape(3,-1)\n", "target"]}, {"cell_type": "code", "execution_count": 77, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([12, 15, 18])"]}, "execution_count": 77, "metadata": {}, "output_type": "execute_result"}], "source": ["target.sum(0)"]}, {"cell_type": "code", "execution_count": 78, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([ 6, 15, 24])"]}, "execution_count": 78, "metadata": {}, "output_type": "execute_result"}], "source": ["target.sum(1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5. 广播机制\n", "\n", "广播机制用于处理两个不同维度数组之间的操作，这里只讨论不超过两维的数组广播机制。\n", "\n", "【a】标量和数组的操作\n", "\n", "当一个标量和数组进行运算时，标量会自动把大小扩充为数组大小，之后进行逐元素操作："]}, {"cell_type": "code", "execution_count": 79, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[4., 4.],\n", "       [4., 4.]])"]}, "execution_count": 79, "metadata": {}, "output_type": "execute_result"}], "source": ["res = 3 * np.ones((2,2)) + 1\n", "res"]}, {"cell_type": "code", "execution_count": 80, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[0.25, 0.25],\n", "       [0.25, 0.25]])"]}, "execution_count": 80, "metadata": {}, "output_type": "execute_result"}], "source": ["res = 1 / res\n", "res"]}, {"cell_type": "markdown", "metadata": {}, "source": ["【b】二维数组之间的操作\n", "\n", "当两个数组维度完全一致时，使用对应元素的操作，否则会报错，除非其中的某个数组的维度是$m×1$或者$1×n$，那么会扩充其具有$1$的维度为另一个数组对应维度的大小。例如，$1×2$数组和$3×2$数组做逐元素运算时会把第一个数组扩充为$3×2$，扩充时的对应数值进行赋值。但是，需要注意的是，如果第一个数组的维度是$1×3$，那么由于在第二维上的大小不匹配且不为$1$，此时报错。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 数组的扩充机制"]}, {"cell_type": "code", "execution_count": 81, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[1., 1.],\n", "       [1., 1.],\n", "       [1., 1.]])"]}, "execution_count": 81, "metadata": {}, "output_type": "execute_result"}], "source": ["res = np.ones((3,2))\n", "res"]}, {"cell_type": "markdown", "metadata": {}, "source": ["常见的两个操作是"]}, {"cell_type": "code", "execution_count": 82, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[2., 3.],\n", "       [2., 3.],\n", "       [2., 3.]])"]}, "execution_count": 82, "metadata": {}, "output_type": "execute_result"}], "source": ["res * np.array([[2,3]]) # 第二个数组扩充第一维度为3"]}, {"cell_type": "code", "execution_count": 83, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[2., 2.],\n", "       [3., 3.],\n", "       [4., 4.]])"]}, "execution_count": 83, "metadata": {}, "output_type": "execute_result"}], "source": ["res * np.array([[2],[3],[4]]) # 第二个数组扩充第二维度为2"]}, {"cell_type": "code", "execution_count": 84, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[2., 2.],\n", "       [2., 2.],\n", "       [2., 2.]])"]}, "execution_count": 84, "metadata": {}, "output_type": "execute_result"}], "source": ["res * np.array([[2]]) # 等价于两次扩充，第二个数组两个维度分别扩充为3和2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["【c】一维数组与二维数组的操作\n", "\n", "当一维数组$A_k$与二维数组$B_{m,n}$操作时，等价于把一维数组视作$A_{1,k}$的二维数组，使用的广播法则与【b】中一致，当$k!=n$且$k,n$都不是$1$时报错。"]}, {"cell_type": "code", "execution_count": 85, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[2., 2., 2.],\n", "       [2., 2., 2.]])"]}, "execution_count": 85, "metadata": {}, "output_type": "execute_result"}], "source": ["np.ones(3) + np.ones((2,3))"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[2., 2., 2.],\n", "       [2., 2., 2.]])"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["np.ones((1,3)) + np.ones((2,1))"]}, {"cell_type": "code", "execution_count": 87, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[2., 2., 2.],\n", "       [2., 2., 2.]])"]}, "execution_count": 87, "metadata": {}, "output_type": "execute_result"}], "source": ["np.ones(1) + np.ones((2,3))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 6. 向量与矩阵的计算\n", "【a】向量内积：`dot`\n", "\n", "$$\\rm \\mathbf{a}\\cdot\\mathbf{b} = \\sum_ia_ib_i$$"]}, {"cell_type": "code", "execution_count": 88, "metadata": {}, "outputs": [{"data": {"text/plain": ["22"]}, "execution_count": 88, "metadata": {}, "output_type": "execute_result"}], "source": ["a = np.array([1,2,3])\n", "b = np.array([1,3,5])\n", "a.dot(b)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["【b】向量范数和矩阵范数：`np.linalg.norm`\n", "\n", "在矩阵范数的计算中，最重要的是`ord`参数，可选值如下："]}, {"cell_type": "markdown", "metadata": {}, "source": ["| ord | norm for matrices | norm for vectors |\n", "| :---- | ----: | ----: |\n", "| None   | Frobenius norm | 2-norm |\n", "| 'fro'  | Frobenius norm  | / |\n", "| 'nuc'  | nuclear norm    | / |\n", "| inf    | max(sum(abs(x), axis=1))   | max(abs(x)) |\n", "| -inf   | min(sum(abs(x), axis=1))  |  min(abs(x)) |\n", "| 0      | /   |  sum(x != 0) |\n", "| 1      | max(sum(abs(x), axis=0))  |  as below |\n", "| -1     | min(sum(abs(x), axis=0))   |  as below |\n", "| 2      | 2-norm (largest sing. value) | as below |\n", "| -2     | smallest singular value    | as below |\n", "| other  | /   | sum(abs(x)\\*\\*ord)\\*\\*(1./ord) |"]}, {"cell_type": "code", "execution_count": 89, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[0, 1],\n", "       [2, 3]])"]}, "execution_count": 89, "metadata": {}, "output_type": "execute_result"}], "source": ["matrix_target =  np.arange(4).reshape(-1,2)\n", "matrix_target"]}, {"cell_type": "code", "execution_count": 90, "metadata": {}, "outputs": [{"data": {"text/plain": ["3.7416573867739413"]}, "execution_count": 90, "metadata": {}, "output_type": "execute_result"}], "source": ["np.linalg.norm(matrix_target, 'fro')"]}, {"cell_type": "code", "execution_count": 91, "metadata": {}, "outputs": [{"data": {"text/plain": ["5.0"]}, "execution_count": 91, "metadata": {}, "output_type": "execute_result"}], "source": ["np.linalg.norm(matrix_target, np.inf)"]}, {"cell_type": "code", "execution_count": 92, "metadata": {}, "outputs": [{"data": {"text/plain": ["3.702459173643833"]}, "execution_count": 92, "metadata": {}, "output_type": "execute_result"}], "source": ["np.linalg.norm(matrix_target, 2)"]}, {"cell_type": "code", "execution_count": 93, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0, 1, 2, 3])"]}, "execution_count": 93, "metadata": {}, "output_type": "execute_result"}], "source": ["vector_target =  np.arange(4)\n", "vector_target"]}, {"cell_type": "code", "execution_count": 94, "metadata": {}, "outputs": [{"data": {"text/plain": ["3.0"]}, "execution_count": 94, "metadata": {}, "output_type": "execute_result"}], "source": ["np.linalg.norm(vector_target, np.inf)"]}, {"cell_type": "code", "execution_count": 95, "metadata": {}, "outputs": [{"data": {"text/plain": ["3.7416573867739413"]}, "execution_count": 95, "metadata": {}, "output_type": "execute_result"}], "source": ["np.linalg.norm(vector_target, 2)"]}, {"cell_type": "code", "execution_count": 96, "metadata": {}, "outputs": [{"data": {"text/plain": ["3.3019272488946263"]}, "execution_count": 96, "metadata": {}, "output_type": "execute_result"}], "source": ["np.linalg.norm(vector_target, 3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["【c】矩阵乘法：`@`\n", "\n", "$$\\rm [\\mathbf{A}_{m\\times p}\\mathbf{B}_{p\\times n}]_{ij} = \\sum_{k=1}^p\\mathbf{A}_{ik}\\mathbf{B}_{kj}$$"]}, {"cell_type": "code", "execution_count": 97, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[0, 1],\n", "       [2, 3]])"]}, "execution_count": 97, "metadata": {}, "output_type": "execute_result"}], "source": ["a = np.arange(4).reshape(-1,2)\n", "a"]}, {"cell_type": "code", "execution_count": 98, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[-4, -3],\n", "       [-2, -1]])"]}, "execution_count": 98, "metadata": {}, "output_type": "execute_result"}], "source": ["b = np.arange(-4,0).reshape(-1,2)\n", "b"]}, {"cell_type": "code", "execution_count": 99, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[ -2,  -1],\n", "       [-14,  -9]])"]}, "execution_count": 99, "metadata": {}, "output_type": "execute_result"}], "source": ["a@b"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 三、练习\n", "### Ex1：利用列表推导式写矩阵乘法\n", "一般的矩阵乘法根据公式，可以由三重循环写出，请将其改写为列表推导式的形式。"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import numpy as np\n", "M1 = np.random.rand(2,3)\n", "M2 = np.random.rand(3,4)\n", "res = np.empty((M1.shape[0],M2.shape[1]))\n", "for i in range(M1.shape[0]):\n", "    for j in range(M2.shape[1]):\n", "        item = 0\n", "        for k in range(M1.shape[1]):\n", "            item += M1[i][k] * M2[k][j]\n", "        res[i][j] = item\n", "(np.abs((M1@M2 - res) < 1e-15)).all() # 排除数值误差"]}, {"cell_type": "markdown", "metadata": {}, "source": ["解答注意, list 层级"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["[array([[0.74496351, 0.61966171, 0.55474558, 0.34049775],\n", "        [0.65610602, 0.61853562, 0.47500698, 0.3479578 ]]),\n", " array([[0.74496351, 0.61966171, 0.55474558, 0.34049775],\n", "        [0.65610602, 0.61853562, 0.47500698, 0.3479578 ]])]"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["import numpy as np\n", "M1 = np.random.rand(2,3)\n", "M2 = np.random.rand(3,4)\n", "res = np.array([[sum(M1[i][k]*M2[k][j] for k in range(M1.shape[1])) for j in range(M2.shape[1])] for i in range(M1.shape[0])])\n", "[res,M1@M2]"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["### Ex2：更新矩阵\n", "设矩阵 $A_{m×n}$ ，现在对 $A$ 中的每一个元素进行更新生成矩阵 $B$ ，更新方法是 $B_{ij}=A_{ij}\\sum_{k=1}^n\\frac{1}{A_{ik}}$ ，例如下面的矩阵为 $A$ ，则 $B_{2,2}=5\\times(\\frac{1}{4}+\\frac{1}{5}+\\frac{1}{6})=\\frac{37}{12}$ ，请利用 `Numpy` 高效实现。\n", "$$\\begin{split}A=\\left[ \\begin{matrix} 1 & 2 &3\\\\4&5&6\\\\7&8&9 \\end{matrix} \\right]\\end{split}$$"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[1.83333333, 3.66666667, 5.5       ],\n", "       [2.46666667, 3.08333333, 3.7       ],\n", "       [2.65277778, 3.03174603, 3.41071429]])"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["import numpy as np\n", "A=np.linspace(1,9,9).reshape(-1,3)\n", "C=1/A\n", "np.array([[sum(C[i][j] for j in range(C.shape[1]))for k in range(3)]for i in range(C.shape[0])])*A"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Ex3：卡方统计量\n", "\n", "设矩阵$A_{m\\times n}$，记$B_{ij} = \\frac{(\\sum_{i=1}^mA_{ij})\\times (\\sum_{j=1}^nA_{ij})}{\\sum_{i=1}^m\\sum_{j=1}^nA_{ij}}$，定义卡方值如下：\n", "$$\\chi^2 = \\sum_{i=1}^m\\sum_{j=1}^n\\frac{(A_{ij}-B_{ij})^2}{B_{ij}}$$\n", "请利用`Numpy`对给定的矩阵$A$计算$\\chi^2$ "]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/plain": ["11.842696601945802"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["import numpy as np\n", "np.random.seed(0)\n", "A = np.random.randint(10, 20, (8, 5))\n", "stM=np.ones((8,5)) #用于数组扩增\n", "B1=stM*(np.array([sum([A[i][j] for i in range(A.shape[0])]) for j in range(A.shape[1])]))\n", "B2=stM*(np.array([[sum([A[i][j] for j in range(A.shape[1])])] for i in range(A.shape[0])]))\n", "B=(B1*B2)/A.sum()\n", "(((A-B)**2)/B).sum()\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[1., 1., 1., 1., 1.],\n", "       [1., 1., 1., 1., 1.],\n", "       [1., 1., 1., 1., 1.],\n", "       [1., 1., 1., 1., 1.],\n", "       [1., 1., 1., 1., 1.],\n", "       [1., 1., 1., 1., 1.],\n", "       [1., 1., 1., 1., 1.],\n", "       [1., 1., 1., 1., 1.]])"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["np.ones((8,5))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Ex4：改进矩阵计算的性能\n", "设$Z$为$m×n$的矩阵，$B$和$U$分别是$m×p$和$p×n$的矩阵，$B_i$为$B$的第$i$行，$U_j$为$U$的第$j$列，下面定义$\\displaystyle R=\\sum_{i=1}^m\\sum_{j=1}^n\\|B_i-U_j\\|_2^2Z_{ij}$，其中$\\|\\mathbf{a}\\|_2^2$表示向量$a$的分量平方和$\\sum_i a_i^2$。\n", "\n", "现有某人根据如下给定的样例数据计算$R$的值，请充分利用`Numpy`中的函数，基于此问题改进这段代码的性能。"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[array([[0, 1, 1, ..., 1, 0, 1],\n", "       [0, 1, 1, ..., 1, 1, 0],\n", "       [1, 0, 0, ..., 1, 1, 1],\n", "       ...,\n", "       [1, 0, 0, ..., 1, 1, 0],\n", "       [0, 0, 0, ..., 1, 1, 0],\n", "       [0, 1, 1, ..., 1, 1, 1]]), array([[1, 0, 1, ..., 1, 1, 0],\n", "       [0, 0, 0, ..., 0, 0, 1],\n", "       [0, 1, 0, ..., 0, 1, 1],\n", "       ...,\n", "       [1, 0, 1, ..., 1, 0, 1],\n", "       [0, 0, 1, ..., 0, 0, 0],\n", "       [1, 1, 0, ..., 0, 0, 1]]), array([[1, 0, 0, ..., 1, 0, 0],\n", "       [0, 1, 1, ..., 1, 1, 0],\n", "       [1, 0, 1, ..., 0, 1, 1],\n", "       ...,\n", "       [0, 1, 0, ..., 1, 1, 0],\n", "       [1, 0, 0, ..., 1, 1, 0],\n", "       [0, 0, 0, ..., 0, 0, 1]]), 100566]\n"]}], "source": ["import numpy as np\n", "np.random.seed(0)\n", "m, n, p = 100, 80, 50\n", "B = np.random.randint(0, 2, (m, p))\n", "U = np.random.randint(0, 2, (p, n))\n", "Z = np.random.randint(0, 2, (m, n))\n", "def solution(B=B, U=U, Z=Z):\n", "    L_res = []\n", "    for i in range(m):\n", "        for j in range(n):\n", "            norm_value = ((B[i]-U[:,j])**2).sum()\n", "            L_res.append(norm_value*Z[i][j])\n", "    return sum(L_res)\n", "print([B,U,Z,solution(B, U, Z)])"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["29.4 ms ± 181 µs per loop (mean ± std. dev. of 7 runs, 30 loops each)\n"]}], "source": ["%timeit -n 30 solution(B, U, Z)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["33.7 ms ± 426 µs per loop (mean ± std. dev. of 7 runs, 30 loops each)\n"]}], "source": ["%timeit -n 30 ((np.array([[np.linalg.norm(B[i]-U[:,j]) for j in range(U.shape[1])] for i in range(B.shape[0])])**2)*Z).sum()"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/plain": ["100566.0"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["import numpy as np\n", "np.random.seed(0)\n", "m, n, p = 100, 80, 50\n", "B = np.random.randint(0, 2, (m, p))\n", "U = np.random.randint(0, 2, (p, n))\n", "Z = np.random.randint(0, 2, (m, n))\n", "((np.array([[np.linalg.norm(B[i]-U[:,j]) for j in range(U.shape[1])] for i in range(B.shape[0])])**2)*Z).sum()"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["(100, 80)"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["Z.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Ex5：连续整数的最大长度\n", "\n", "输入一个整数的`Numpy`数组，返回其中严格递增连续整数子数组的最大长度，正向是指递增方向。例如，输入\\[1,2,5,6,7\\]，\\[5,6,7\\]为具有最大长度的连续整数子数组，因此输出3；输入\\[3,2,1,2,3,4,6\\]，\\[1,2,3,4\\]为具有最大长度的连续整数子数组，因此输出4。请充分利用`Numpy`的内置函数完成。（提示：考虑使用`nonzero, diff`函数）"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([1, 0, 1, 1])"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["import numpy as np\n", "def find_conti(serie):\n", "    find1=np.diff(serie)\n", "    conti= np.where(find1==1 ,find1, 0)\n", "    return\n", "find_conti(np.array([1,2,5,6,7]))\n", "    "]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([1, 4], dtype=int64)"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import numpy as np\n", "test=np.array([3,2,1,2,3,4,6])\n", "np.diff(np.nonzero(np.diff(test)!=1)[0])\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.20"}, "orig_nbformat": 2}, "nbformat": 4, "nbformat_minor": 2}