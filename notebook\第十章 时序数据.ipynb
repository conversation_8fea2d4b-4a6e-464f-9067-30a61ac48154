{"metadata": {"language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.9-final"}, "orig_nbformat": 2, "kernelspec": {"name": "python3", "display_name": "Python 3.7.9 64-bit ('ML': conda)", "metadata": {"interpreter": {"hash": "539eeca01c4a82e9e0248c7d96d3e95516d644c0bbc096dd3e480eeded1964cf"}}}}, "nbformat": 4, "nbformat_minor": 2, "cells": [{"source": ["<center><h1>第十章 时序数据</h1></center>"], "cell_type": "markdown", "metadata": {}}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd"]}, {"source": ["## 一、时序中的基本对象\n", "\n", "时间序列的概念在日常生活中十分常见，但对于一个具体的时序事件而言，可以从多个时间对象的角度来描述。例如2020年9月7日周一早上8点整需要到教室上课，这个课会在当天早上10点结束，其中包含了哪些时间概念？\n", "\n", "* 第一，会出现时间戳（Date times）的概念，即'2020-9-7 08:00:00'和'2020-9-7 10:00:00'这两个时间点分别代表了上课和下课的时刻，在`pandas`中称为`Timestamp`。同时，一系列的时间戳可以组成`DatetimeIndex`，而将它放到`Series`中后，`Series`的类型就变为了`datetime64[ns]`，如果有涉及时区则为`datetime64[ns, tz]`，其中tz是timezone的简写。\n", "\n", "* 第二，会出现时间差（Time deltas）的概念，即上课需要的时间，两个`Timestamp`做差就得到了时间差，pandas中利用`Timedelta`来表示。类似的，一系列的时间差就组成了`TimedeltaIndex`， 而将它放到`Series`中后，`Series`的类型就变为了`timedelta64[ns]`。\n", "\n", "* 第三，会出现时间段（Time spans）的概念，即在8点到10点这个区间都会持续地在上课，在`pandas`利用`Period`来表示。类似的，一系列的时间段就组成了`PeriodIndex`， 而将它放到`Series`中后，`Series`的类型就变为了`Period`。\n", "\n", "* 第四，会出现日期偏置（Date offsets）的概念，假设你只知道9月的第一个周一早上8点要去上课，但不知道具体的日期，那么就需要一个类型来处理此类需求。再例如，想要知道2020年9月7日后的第30个工作日是哪一天，那么时间差就解决不了你的问题，从而`pandas`中的`DateOffset`就出现了。同时，`pandas`中没有为一列时间偏置专门设计存储类型，理由也很简单，因为需求比较奇怪，一般来说我们只需要对一批时间特征做一个统一的特殊日期偏置。\n", "\n", "通过这个简单的例子，就能够容易地总结出官方文档中的这个[表格](https://pandas.pydata.org/docs/user_guide/timeseries.html#overview)：\n", "\n", "|概念 |                          单元素类型              |    数组类型         |                pandas数据类型|\n", "|:---------|:----------|:-----------|:------------|\n", "|Date times           |           `Timestamp`       |       `DatetimeIndex`  |   `datetime64[ns]`|\n", "|Time deltas          |           `Timedelta`        |      `TimedeltaIndex` |  `timedelta64[ns]`|\n", "|Time spans            |          `Period`           |      `PeriodIndex`   |    `period[freq]`|\n", "|Date offsets          |          `DateOffset`         |    `None`          |    `None`|\n", "\n", "由于时间段对象`Period/PeriodIndex`的使用频率并不高，因此将不进行讲解，而只涉及时间戳序列、时间差序列和日期偏置的相关内容。\n", "\n", "## 二、时间戳\n", "### 1. Timestamp的构造与属性\n", "\n", "单个时间戳的生成利用`pd.Timestamp`实现，一般而言的常见日期格式都能被成功地转换：\n"], "cell_type": "markdown", "metadata": {}}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Timestamp('2020-01-01 00:00:00')"]}, "metadata": {}, "execution_count": 2}], "source": ["ts = pd.Timestamp('2020/1/1')\n", "ts"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Timestamp('2020-01-01 08:10:30')"]}, "metadata": {}, "execution_count": 3}], "source": ["ts = pd.Timestamp('2020-1-1 08:10:30')\n", "ts"]}, {"source": ["通过`year, month, day, hour, min, second`可以获取具体的数值："], "cell_type": "markdown", "metadata": {}}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["2020"]}, "metadata": {}, "execution_count": 4}], "source": ["ts.year"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["1"]}, "metadata": {}, "execution_count": 5}], "source": ["ts.month"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["1"]}, "metadata": {}, "execution_count": 6}], "source": ["ts.day"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["8"]}, "metadata": {}, "execution_count": 7}], "source": ["ts.hour"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["10"]}, "metadata": {}, "execution_count": 8}], "source": ["ts.minute"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["30"]}, "metadata": {}, "execution_count": 9}], "source": ["ts.second"]}, {"source": ["在`pandas`中，时间戳的最小精度为纳秒`ns`，由于使用了64位存储，可以表示的时间范围大约可以如下计算：\n", "$$\\rm Time\\,Range = \\frac{2^{64}}{10^9\\times 60\\times 60\\times 24\\times 365} \\approx 585 (Years)$$\n", "通过`pd.Timestamp.max`和`pd.Timestamp.min`可以获取时间戳表示的范围，可以看到确实表示的区间年数大小正如上述计算结果："], "cell_type": "markdown", "metadata": {}}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Timestamp('2262-04-11 23:47:16.854775807')"]}, "metadata": {}, "execution_count": 10}], "source": ["pd.Timestamp.max"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Timestamp('1677-09-21 00:12:43.145225')"]}, "metadata": {}, "execution_count": 11}], "source": ["pd.Timestamp.min"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["585"]}, "metadata": {}, "execution_count": 12}], "source": ["pd.Timestamp.max.year - pd.Timestamp.min.year"]}, {"source": ["### 2. Datetime序列的生成\n", "\n", "一组时间戳可以组成时间序列，可以用`to_datetime`和`date_range`来生成。其中，`to_datetime`能够把一列时间戳格式的对象转换成为`datetime64[ns]`类型的时间序列："], "cell_type": "markdown", "metadata": {}}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["0   2019-10-05\n", "1   2019-09-04\n", "2   2019-09-12\n", "3   2020-01-03\n", "4   2019-11-06\n", "Name: Test_Date, dtype: datetime64[ns]"]}, "metadata": {}, "execution_count": 13}], "source": ["pd.to_datetime(['2020-1-1', '2020-1-3', '2020-1-6'])\n", "df = pd.read_csv('../data/learn_pandas.csv')\n", "s = pd.to_datetime(df.Test_Date)\n", "s.head()"]}, {"source": ["在极少数情况，时间戳的格式不满足转换时，可以强制使用`format`进行匹配："], "cell_type": "markdown", "metadata": {}}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["DatetimeIndex(['2020-01-01', '2020-01-03'], dtype='datetime64[ns]', freq=None)"]}, "metadata": {}, "execution_count": 14}], "source": ["temp = pd.to_datetime(['2020\\\\1\\\\1','2020\\\\1\\\\3'],format='%Y\\\\%m\\\\%d')\n", "temp"]}, {"source": ["注意上面由于传入的是列表，而非`pandas`内部的`Series`，因此返回的是`DatetimeIndex`，如果想要转为`datetime64[ns]`的序列，需要显式用`Series`转化："], "cell_type": "markdown", "metadata": {}}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["0   2020-01-01\n", "1   2020-01-03\n", "dtype: datetime64[ns]"]}, "metadata": {}, "execution_count": 15}], "source": ["pd.Series(temp).head()"]}, {"source": ["另外，还存在一种把表的多列时间属性拼接转为时间序列的`to_datetime`操作，此时的列名必须和以下给定的时间关键词列名一致："], "cell_type": "markdown", "metadata": {}}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["0   2020-01-01 10:30:20\n", "1   2020-01-02 20:50:40\n", "dtype: datetime64[ns]"]}, "metadata": {}, "execution_count": 16}], "source": ["df_date_cols = pd.DataFrame({'year': [2020, 2020],\n", "                             'month': [1, 1],\n", "                             'day': [1, 2],\n", "                             'hour': [10, 20],\n", "                             'minute': [30, 50],\n", "                             'second': [20, 40]})\n", "pd.to_datetime(df_date_cols)"]}, {"source": ["`date_range`是一种生成连续间隔时间的一种方法，其重要的参数为`start, end, freq, periods`，它们分别表示开始时间，结束时间，时间间隔，时间戳个数。其中，四个中的三个参数决定了，那么剩下的一个就随之确定了。这里要注意，开始或结束日期如果作为端点则它会被包含："], "cell_type": "markdown", "metadata": {}}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["DatetimeIndex(['2020-01-01', '2020-01-11', '2020-01-21'], dtype='datetime64[ns]', freq='10D')"]}, "metadata": {}, "execution_count": 17}], "source": ["pd.date_range('2020-1-1','2020-1-21', freq='10D') # 包含"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["DatetimeIndex(['2020-01-01', '2020-01-11', '2020-01-21', '2020-01-31',\n", "               '2020-02-10', '2020-02-20'],\n", "              dtype='datetime64[ns]', freq='10D')"]}, "metadata": {}, "execution_count": 18}], "source": ["pd.date_range('2020-1-1','2020-2-28', freq='10D')"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["DatetimeIndex(['2020-01-01 00:00:00', '2020-01-12 14:24:00',\n", "               '2020-01-24 04:48:00', '2020-02-04 19:12:00',\n", "               '2020-02-16 09:36:00', '2020-02-28 00:00:00'],\n", "              dtype='datetime64[ns]', freq=None)"]}, "metadata": {}, "execution_count": 19}], "source": ["pd.date_range('2020-1-1', '2020-2-28', periods=6) # 由于结束日期无法取到，freq不为10天"]}, {"source": ["这里的`freq`参数与`DateOffset`对象紧密相关，将在第四节介绍其具体的用法。\n", "#### 【练一练】\n", "`Timestamp`上定义了一个`value`属性，其返回的整数值代表了从1970年1月1日零点到给定时间戳相差的纳秒数，请利用这个属性构造一个随机生成给定日期区间内日期序列的函数。\n", "#### 【END】\n", "最后，要介绍一种改变序列采样频率的方法`asfreq`，它能够根据给定的`freq`对序列进行类似于`reindex`的操作："], "cell_type": "markdown", "metadata": {}}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["2020-01-01    0.393911\n", "2020-01-03    0.703650\n", "2020-01-05    0.650046\n", "2020-01-07    0.726203\n", "2020-01-09    0.126783\n", "dtype: float64"]}, "metadata": {}, "execution_count": 20}], "source": ["s = pd.Series(np.random.rand(5), index=pd.to_datetime(['2020-1-%d'%i for i in range(1,10,2)]))\n", "s.head()"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["2020-01-01    0.393911\n", "2020-01-02         NaN\n", "2020-01-03    0.703650\n", "2020-01-04         NaN\n", "2020-01-05    0.650046\n", "Freq: D, dtype: float64"]}, "metadata": {}, "execution_count": 21}], "source": ["s.asfreq('D').head()"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["2020-01-01 00:00:00    0.393911\n", "2020-01-01 12:00:00         NaN\n", "2020-01-02 00:00:00         NaN\n", "2020-01-02 12:00:00         NaN\n", "2020-01-03 00:00:00    0.703650\n", "Freq: 12H, dtype: float64"]}, "metadata": {}, "execution_count": 22}], "source": ["s.asfreq('12H').head()"]}, {"source": ["#### 【NOTE】\n", "前面提到了`datetime64[ns]`本质上可以理解为一个大整数，对于一个该类型的序列，可以使用`max, min, mean`，来取得最大时间戳、最小时间戳和“平均”时间戳。\n", "#### 【END】\n", "### 3. dt对象\n", "\n", "如同`category, string`的序列上定义了`cat, str`来完成分类数据和文本数据的操作，在时序类型的序列上定义了`dt`对象来完成许多时间序列的相关操作。这里对于`datetime64[ns]`类型而言，可以大致分为三类操作：取出时间相关的属性、判断时间戳是否满足条件、取整操作。\n", "\n", "第一类操作的常用属性包括：`date, time, year, month, day, hour, minute, second, microsecond, nanosecond, dayofweek, dayofyear, weekofyear, daysinmonth, quarter`，其中`daysinmonth, quarter`分别表示该月一共有几天和季度。"], "cell_type": "markdown", "metadata": {}}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["0    2020-01-01\n", "1    2020-01-02\n", "2    2020-01-03\n", "dtype: object"]}, "metadata": {}, "execution_count": 23}], "source": ["s = pd.Series(pd.date_range('2020-1-1','2020-1-3', freq='D'))\n", "s.dt.date"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["0    00:00:00\n", "1    00:00:00\n", "2    00:00:00\n", "dtype: object"]}, "metadata": {}, "execution_count": 24}], "source": ["s.dt.time"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["0    1\n", "1    2\n", "2    3\n", "dtype: int64"]}, "metadata": {}, "execution_count": 25}], "source": ["s.dt.day"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["0    31\n", "1    31\n", "2    31\n", "dtype: int64"]}, "metadata": {}, "execution_count": 26}], "source": ["s.dt.<PERSON><PERSON><PERSON><PERSON>"]}, {"source": ["在这些属性中，经常使用的是`dayofweek`，它返回了周中的星期情况，周一为0、周二为1，以此类推："], "cell_type": "markdown", "metadata": {}}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["0    2\n", "1    3\n", "2    4\n", "dtype: int64"]}, "metadata": {}, "execution_count": 27}], "source": ["s.dt.dayofweek"]}, {"source": ["此外，可以通过`month_name, day_name`返回英文的月名和星期名，注意它们是方法而不是属性："], "cell_type": "markdown", "metadata": {}}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["0    January\n", "1    January\n", "2    January\n", "dtype: object"]}, "metadata": {}, "execution_count": 28}], "source": ["s.dt.month_name()"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["0    Wednesday\n", "1     Thursday\n", "2       Friday\n", "dtype: object"]}, "metadata": {}, "execution_count": 29}], "source": ["s.dt.day_name()"]}, {"source": ["第二类判断操作主要用于测试是否为月/季/年的第一天或者最后一天："], "cell_type": "markdown", "metadata": {}}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["0     True\n", "1    False\n", "2    False\n", "dtype: bool"]}, "metadata": {}, "execution_count": 30}], "source": ["s.dt.is_year_start # 还可选 is_quarter/month_start"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["0    False\n", "1    False\n", "2    False\n", "dtype: bool"]}, "metadata": {}, "execution_count": 31}], "source": ["s.dt.is_year_end # 还可选 is_quarter/month_end"]}, {"source": ["第三类的取整操作包含`round, ceil, floor`，它们的公共参数为`freq`，常用的包括`H, min, S`（小时、分钟、秒），所有可选的`freq`可参考[此处](https://pandas.pydata.org/docs/user_guide/timeseries.html#offset-aliases>)。"], "cell_type": "markdown", "metadata": {}}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["0   2020-01-01 20:35:00\n", "1   2020-01-01 21:20:00\n", "2   2020-01-01 22:05:00\n", "dtype: datetime64[ns]"]}, "metadata": {}, "execution_count": 32}], "source": ["s = pd.Series(pd.date_range('2020-1-1 20:35:00', '2020-1-1 22:35:00', freq='45min'))\n", "s"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["0   2020-01-01 21:00:00\n", "1   2020-01-01 21:00:00\n", "2   2020-01-01 22:00:00\n", "dtype: datetime64[ns]"]}, "metadata": {}, "execution_count": 33}], "source": ["s.dt.round('1H')"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["0   2020-01-01 21:00:00\n", "1   2020-01-01 22:00:00\n", "2   2020-01-01 23:00:00\n", "dtype: datetime64[ns]"]}, "metadata": {}, "execution_count": 34}], "source": ["s.dt.ceil('1H')"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["0   2020-01-01 20:00:00\n", "1   2020-01-01 21:00:00\n", "2   2020-01-01 22:00:00\n", "dtype: datetime64[ns]"]}, "metadata": {}, "execution_count": 35}], "source": ["s.dt.floor('1H')"]}, {"source": ["### 4. 时间戳的切片与索引\n", "\n", "一般而言，时间戳序列作为索引使用。如果想要选出某个子时间戳序列，第一类方法是利用`dt`对象和布尔条件联合使用，另一种方式是利用切片，后者常用于连续时间戳。下面，举一些例子说明："], "cell_type": "markdown", "metadata": {}}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["2020-01-01    0\n", "2020-01-02    1\n", "2020-01-03    1\n", "2020-01-04    0\n", "2020-01-05    0\n", "Freq: D, dtype: int32"]}, "metadata": {}, "execution_count": 36}], "source": ["s = pd.Series(np.random.randint(2,size=366), index=pd.date_range('2020-01-01','2020-12-31'))\n", "idx = pd.Series(s.index).dt\n", "s.head()"]}, {"source": ["Example1：每月的第一天或者最后一天"], "cell_type": "markdown", "metadata": {}}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["2020-01-01    0\n", "2020-01-31    1\n", "2020-02-01    1\n", "2020-02-29    1\n", "2020-03-01    0\n", "dtype: int32"]}, "metadata": {}, "execution_count": 37}], "source": ["s[(idx.is_month_start|idx.is_month_end).values].head()"]}, {"source": ["Example2：双休日"], "cell_type": "markdown", "metadata": {}}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["2020-01-04    0\n", "2020-01-05    0\n", "2020-01-11    1\n", "2020-01-12    0\n", "2020-01-18    0\n", "dtype: int32"]}, "metadata": {}, "execution_count": 38}], "source": ["s[idx.dayofweek.isin([5,6]).values].head()"]}, {"source": ["Example3：取出单日值"], "cell_type": "markdown", "metadata": {}}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["0"]}, "metadata": {}, "execution_count": 39}], "source": ["s['2020-01-01']"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["0"]}, "metadata": {}, "execution_count": 40}], "source": ["s['20200101'] # 自动转换标准格式"]}, {"source": ["Example4：取出七月"], "cell_type": "markdown", "metadata": {}}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["2020-07-01    1\n", "2020-07-02    1\n", "2020-07-03    0\n", "2020-07-04    1\n", "2020-07-05    0\n", "Freq: D, dtype: int32"]}, "metadata": {}, "execution_count": 41}], "source": ["s['2020-07'].head()"]}, {"source": ["Example5：取出5月初至7月15日"], "cell_type": "markdown", "metadata": {}}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["2020-05-01    1\n", "2020-05-02    1\n", "2020-05-03    1\n", "2020-05-04    1\n", "2020-05-05    1\n", "Freq: D, dtype: int32"]}, "metadata": {}, "execution_count": 42}], "source": ["s['2020-05':'2020-7-15'].head()"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["2020-07-11    1\n", "2020-07-12    1\n", "2020-07-13    1\n", "2020-07-14    1\n", "2020-07-15    1\n", "Freq: D, dtype: int32"]}, "metadata": {}, "execution_count": 43}], "source": ["s['2020-05':'2020-7-15'].tail()"]}, {"source": ["## 三、时间差\n", "### 1. <PERSON><PERSON><PERSON>的生成\n", "\n", "正如在第一节中所说，时间差可以理解为两个时间戳的差，这里也可以通过`pd.Timedelta`来构造："], "cell_type": "markdown", "metadata": {}}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Timedelta('1 days 00:25:00')"]}, "metadata": {}, "execution_count": 44}], "source": ["pd.Timestamp('20200102 08:00:00')-pd.Timestamp('20200101 07:35:00')"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Timedelta('1 days 00:25:00')"]}, "metadata": {}, "execution_count": 45}], "source": ["pd.<PERSON><PERSON><PERSON>(days=1, minutes=25) # 需要注意加s"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Timedelta('1 days 00:25:00')"]}, "metadata": {}, "execution_count": 46}], "source": ["pd.<PERSON><PERSON><PERSON>('1 days 25 minutes') # 字符串生成"]}, {"source": ["生成时间差序列的主要方式是`pd.to_timedelta`，其类型为`timedelta64[ns]`："], "cell_type": "markdown", "metadata": {}}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["0   0 days 00:04:34\n", "1   0 days 00:04:20\n", "2   0 days 00:05:22\n", "3   0 days 00:04:08\n", "4   0 days 00:05:22\n", "Name: Time_Record, dtype: timedelta64[ns]"]}, "metadata": {}, "execution_count": 47}], "source": ["s = pd.to_<PERSON><PERSON><PERSON>(df.Time_Record)\n", "s.head()"]}, {"source": ["与`date_range`一样，时间差序列也可以用`timedelta_range`来生成，它们两者具有一致的参数："], "cell_type": "markdown", "metadata": {}}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["TimedeltaIndex(['0 days 00:00:00', '0 days 00:06:00', '0 days 00:12:00'], dtype='timedelta64[ns]', freq='6T')"]}, "metadata": {}, "execution_count": 48}], "source": ["pd.timedelta_range('0s', '1000s', freq='6min')"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["TimedeltaIndex(['0 days 00:00:00', '0 days 00:08:20', '0 days 00:16:40'], dtype='timedelta64[ns]', freq=None)"]}, "metadata": {}, "execution_count": 49}], "source": ["pd.timedelta_range('0s', '1000s', periods=3)"]}, {"source": ["对于`Timedelta`序列，同样也定义了`dt`对象，上面主要定义了的属性包括`days, seconds, mircroseconds, nanoseconds`，它们分别返回了对应的时间差特征。需要注意的是，这里的`seconds`不是指单纯的秒，而是对天数取余后剩余的秒数："], "cell_type": "markdown", "metadata": {}}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["0    274\n", "1    260\n", "2    322\n", "3    248\n", "4    322\n", "Name: Time_Record, dtype: int64"]}, "metadata": {}, "execution_count": 50}], "source": ["s.dt.seconds.head()"]}, {"source": ["如果不想对天数取余而直接对应秒数，可以使用`total_seconds`"], "cell_type": "markdown", "metadata": {}}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["0    274.0\n", "1    260.0\n", "2    322.0\n", "3    248.0\n", "4    322.0\n", "Name: Time_Record, dtype: float64"]}, "metadata": {}, "execution_count": 51}], "source": ["s.dt.total_seconds().head()"]}, {"source": ["与时间戳序列类似，取整函数也是可以在`dt`对象上使用的："], "cell_type": "markdown", "metadata": {}}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["0   0 days 00:05:00\n", "1   0 days 00:04:00\n", "2   0 days 00:05:00\n", "3   0 days 00:04:00\n", "4   0 days 00:05:00\n", "Name: Time_Record, dtype: timedelta64[ns]"]}, "metadata": {}, "execution_count": 52}], "source": ["pd.to_<PERSON><PERSON><PERSON>(df.Time_Record).dt.round('min').head()"]}, {"source": ["### 2. <PERSON><PERSON><PERSON>的运算\n", "\n", "时间差支持的常用运算有三类：与标量的乘法运算、与时间戳的加减法运算、与时间差的加减法与除法运算："], "cell_type": "markdown", "metadata": {}}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Timedelta('2 days 00:00:00')"]}, "metadata": {}, "execution_count": 53}], "source": ["td1 = pd.<PERSON><PERSON><PERSON>(days=1)\n", "td2 = pd.<PERSON><PERSON><PERSON>(days=3)\n", "ts = pd.Timestamp('20200101')\n", "td1 * 2"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Timedelta('2 days 00:00:00')"]}, "metadata": {}, "execution_count": 54}], "source": ["td2 - td1"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Timestamp('2020-01-02 00:00:00')"]}, "metadata": {}, "execution_count": 55}], "source": ["ts + td1"]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Timestamp('2019-12-31 00:00:00')"]}, "metadata": {}, "execution_count": 56}], "source": ["ts - td1"]}, {"source": ["这些运算都可以移植到时间差的序列上："], "cell_type": "markdown", "metadata": {}}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["TimedeltaIndex(['5 days', '10 days', '15 days', '20 days', '25 days'], dtype='timedelta64[ns]', freq='5D')"]}, "metadata": {}, "execution_count": 57}], "source": ["td1 = pd.timedelta_range(start='1 days', periods=5)\n", "td2 = pd.timedelta_range(start='12 hours', freq='2H', periods=5)\n", "ts = pd.date_range('20200101', '20200105')\n", "td1 * 5"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["0    0 days\n", "1    2 days\n", "2    6 days\n", "3   12 days\n", "4   20 days\n", "dtype: timedelta64[ns]"]}, "metadata": {}, "execution_count": 58}], "source": ["td1 * pd.Series(list(range(5))) # 逐个相乘"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["TimedeltaIndex(['0 days 12:00:00', '1 days 10:00:00', '2 days 08:00:00',\n", "                '3 days 06:00:00', '4 days 04:00:00'],\n", "               dtype='timedelta64[ns]', freq=None)"]}, "metadata": {}, "execution_count": 59}], "source": ["td1 - td2"]}, {"cell_type": "code", "execution_count": 60, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["DatetimeIndex(['2020-01-02', '2020-01-03', '2020-01-04', '2020-01-05',\n", "               '2020-01-06'],\n", "              dtype='datetime64[ns]', freq='D')"]}, "metadata": {}, "execution_count": 60}], "source": ["td1 + pd.Timestamp('20200101')"]}, {"cell_type": "code", "execution_count": 61, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["DatetimeIndex(['2020-01-02', '2020-01-04', '2020-01-06', '2020-01-08',\n", "               '2020-01-10'],\n", "              dtype='datetime64[ns]', freq=None)"]}, "metadata": {}, "execution_count": 61}], "source": ["td1 + ts # 逐个相加"]}, {"source": ["## 四、日期偏置\n", "### 1. Offset对象\n", "\n", "日期偏置是一种和日历相关的特殊时间差，例如回到第一节中的两个问题：如何求2020年9月第一个周一的日期，以及如何求2020年9月7日后的第30个工作日是哪一天。"], "cell_type": "markdown", "metadata": {}}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Timestamp('2020-09-07 00:00:00')"]}, "metadata": {}, "execution_count": 62}], "source": ["pd.Timestamp('20200831') + pd.offsets.WeekOfMonth(week=0,weekday=0)"]}, {"cell_type": "code", "execution_count": 63, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Timestamp('2020-10-19 00:00:00')"]}, "metadata": {}, "execution_count": 63}], "source": ["pd.Timestamp('20200907') + pd.offsets.BDay(30)"]}, {"source": ["从上面的例子中可以看到，`Offset`对象在`pd.offsets`中被定义。当使用`+`时获取离其最近的下一个日期，当使用`-`时获取离其最近的上一个日期："], "cell_type": "markdown", "metadata": {}}, {"cell_type": "code", "execution_count": 64, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Timestamp('2020-08-03 00:00:00')"]}, "metadata": {}, "execution_count": 64}], "source": ["pd.Timestamp('20200831') - pd.offsets.WeekOfMonth(week=0,weekday=0)"]}, {"cell_type": "code", "execution_count": 65, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Timestamp('2020-07-27 00:00:00')"]}, "metadata": {}, "execution_count": 65}], "source": ["pd.Timestamp('20200907') - pd.offsets.BDay(30)"]}, {"cell_type": "code", "execution_count": 66, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Timestamp('2020-09-30 00:00:00')"]}, "metadata": {}, "execution_count": 66}], "source": ["pd.Timestamp('20200907') + pd.offsets.MonthEnd()"]}, {"source": ["常用的日期偏置如下可以查阅这里的[文档](https://pandas.pydata.org/docs/user_guide/timeseries.html#dateoffset-objects)描述。在文档罗列的`Offset`中，需要介绍一个特殊的`Offset`对象`CDay`，其中的`holidays, weekmask`参数能够分别对自定义的日期和星期进行过滤，前者传入了需要过滤的日期列表，后者传入的是三个字母的星期缩写构成的星期字符串，其作用是只保留字符串中出现的星期："], "cell_type": "markdown", "metadata": {}}, {"cell_type": "code", "execution_count": 67, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["2020-01-08    2\n", "2020-01-09    3\n", "2020-01-10    4\n", "2020-01-11    5\n", "Freq: D, dtype: int64"]}, "metadata": {}, "execution_count": 67}], "source": ["my_filter = pd.offsets.CDay(n=1,weekmask='Wed Fri',holidays=['20200109'])\n", "dr = pd.date_range('20200108', '20200111')\n", "dr.to_series().dt.dayofweek"]}, {"cell_type": "code", "execution_count": 68, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[Timestamp('2020-01-10 00:00:00'),\n", " Timestamp('2020-01-10 00:00:00'),\n", " Timestamp('2020-01-15 00:00:00'),\n", " Timestamp('2020-01-15 00:00:00')]"]}, "metadata": {}, "execution_count": 68}], "source": ["[i + my_filter for i in dr]"]}, {"source": ["上面的例子中，`n`表示增加一天`CDay`，`dr`中的第一天为`20200108`，但由于下一天`20200109`被排除了，并且`20200110`是合法的周五，因此转为`20200110`，其他后面的日期处理类似。\n", "#### 【CAUTION】不要使用部分`Offset`\n", "在当前版本下由于一些 ``bug`` ，不要使用 ``Day`` 级别以下的 ``Offset`` 对象，比如 ``Hour, Second`` 等，请使用对应的 ``Timedelta`` 对象来代替。\n", "#### 【END】\n", "### 2. 偏置字符串\n", "\n", "前面提到了关于`date_range`的`freq`取值可用`Offset`对象，同时在`pandas`中几乎每一个`Offset`对象绑定了日期偏置字符串（`frequencies strings/offset aliases`），可以指定`Offset`对应的字符串来替代使用。下面举一些常见的例子。"], "cell_type": "markdown", "metadata": {}}, {"cell_type": "code", "execution_count": 69, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["DatetimeIndex(['2020-01-01', '2020-02-01', '2020-03-01'], dtype='datetime64[ns]', freq='MS')"]}, "metadata": {}, "execution_count": 69}], "source": ["pd.date_range('20200101','20200331', freq='MS') # 月初"]}, {"cell_type": "code", "execution_count": 70, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["DatetimeIndex(['2020-01-31', '2020-02-29', '2020-03-31'], dtype='datetime64[ns]', freq='M')"]}, "metadata": {}, "execution_count": 70}], "source": ["pd.date_range('20200101','20200331', freq='M') # 月末"]}, {"cell_type": "code", "execution_count": 71, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["DatetimeIndex(['2020-01-01', '2020-01-02', '2020-01-03', '2020-01-06',\n", "               '2020-01-07', '2020-01-08', '2020-01-09', '2020-01-10'],\n", "              dtype='datetime64[ns]', freq='B')"]}, "metadata": {}, "execution_count": 71}], "source": ["pd.date_range('20200101','20200110', freq='B') # 工作日"]}, {"cell_type": "code", "execution_count": 72, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["DatetimeIndex(['2020-01-06', '2020-01-13', '2020-01-20', '2020-01-27'], dtype='datetime64[ns]', freq='W-MON')"]}, "metadata": {}, "execution_count": 72}], "source": ["pd.date_range('20200101','20200201', freq='W-MON') # 周一"]}, {"cell_type": "code", "execution_count": 73, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["DatetimeIndex(['2020-01-06'], dtype='datetime64[ns]', freq='WOM-1MON')"]}, "metadata": {}, "execution_count": 73}], "source": ["pd.date_range('20200101','20200201', freq='WOM-1MON') # 每月第一个周一"]}, {"source": ["上面的这些字符串，等价于使用如下的`Offset`对象："], "cell_type": "markdown", "metadata": {}}, {"cell_type": "code", "execution_count": 74, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["DatetimeIndex(['2020-01-01', '2020-02-01', '2020-03-01'], dtype='datetime64[ns]', freq='MS')"]}, "metadata": {}, "execution_count": 74}], "source": ["pd.date_range('20200101','20200331', freq=pd.offsets.MonthBegin())"]}, {"cell_type": "code", "execution_count": 75, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["DatetimeIndex(['2020-01-31', '2020-02-29', '2020-03-31'], dtype='datetime64[ns]', freq='M')"]}, "metadata": {}, "execution_count": 75}], "source": ["pd.date_range('20200101','20200331', freq=pd.offsets.MonthEnd())"]}, {"cell_type": "code", "execution_count": 76, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["DatetimeIndex(['2020-01-01', '2020-01-02', '2020-01-03', '2020-01-06',\n", "               '2020-01-07', '2020-01-08', '2020-01-09', '2020-01-10'],\n", "              dtype='datetime64[ns]', freq='B')"]}, "metadata": {}, "execution_count": 76}], "source": ["pd.date_range('20200101','20200110', freq=pd.offsets.BDay())"]}, {"cell_type": "code", "execution_count": 77, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["DatetimeIndex(['2020-01-06', '2020-01-13', '2020-01-20', '2020-01-27'], dtype='datetime64[ns]', freq='C')"]}, "metadata": {}, "execution_count": 77}], "source": ["pd.date_range('20200101','20200201', freq=pd.offsets.CDay(weekmask='Mon'))"]}, {"cell_type": "code", "execution_count": 78, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["DatetimeIndex(['2020-01-06'], dtype='datetime64[ns]', freq='WOM-1MON')"]}, "metadata": {}, "execution_count": 78}], "source": ["pd.date_range('20200101','20200201', freq=pd.offsets.WeekOfMonth(week=0,weekday=0))"]}, {"source": ["#### 【CAUTION】关于时区问题的说明\n", "各类时间对象的开发，除了使用`python`内置的`datetime`模块，`pandas`还利用了`dateutil`模块，很大一部分是为了处理时区问题。总所周知，我国是没有夏令时调整时间一说的，但有些国家会有这种做法，导致了相对而言一天里可能会有23/24/25个小时，也就是`relativedelta`，这使得`Offset`对象和`Timedelta`对象有了对同一问题处理产生不同结果的现象，其中的规则也较为复杂，官方文档的写法存在部分描述错误，并且难以对描述做出统一修正，因为牵涉到了`Offset`相关的很多组件。因此，本教程完全不考虑时区处理，如果对时区处理的时间偏置有兴趣了解讨论，可以联系我或者参见[这里](https://github.com/pandas-dev/pandas/pull/36516)的讨论。\n", "#### 【END】\n", "## 五、时序中的滑窗与分组\n", "### 1. 滑动窗口\n", "\n", "所谓时序的滑窗函数，即把滑动窗口用`freq`关键词代替，下面给出一个具体的应用案例：在股票市场中有一个指标为`BOLL`指标，它由中轨线、上轨线、下轨线这三根线构成，具体的计算方法分别是`N`日均值线、`N`日均值加两倍`N`日标准差线、`N`日均值减两倍`N`日标准差线。利用`rolling`对象计算`N=30`的`BOLL`指标可以如下写出："], "cell_type": "markdown", "metadata": {}}, {"cell_type": "code", "execution_count": 79, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["2020-01-01   -1\n", "2020-01-02   -2\n", "2020-01-03   -1\n", "2020-01-06   -1\n", "2020-01-07   -2\n", "Freq: B, dtype: int32"]}, "metadata": {}, "execution_count": 79}], "source": ["import matplotlib.pyplot as plt\n", "idx = pd.date_range('20200101', '20201231', freq='B')\n", "np.random.seed(2020)\n", "data = np.random.randint(-1,2,len(idx)).cumsum() # 随机游动构造模拟序列\n", "s = pd.Series(data,index=idx)\n", "s.head()"]}, {"cell_type": "code", "execution_count": 80, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[<matplotlib.lines.Line2D at 0x26941924dc8>]"]}, "metadata": {}, "execution_count": 80}, {"output_type": "display_data", "data": {"text/plain": "<Figure size 432x288 with 1 Axes>", "image/svg+xml": "<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\r\n<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\r\n  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\r\n<!-- Created with matplotlib (https://matplotlib.org/) -->\r\n<svg height=\"263.63625pt\" version=\"1.1\" viewBox=\"0 0 377.**********.63625\" width=\"377.449285pt\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\r\n <metadata>\r\n  <rdf:RDF xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\r\n   <cc:Work>\r\n    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\r\n    <dc:date>2020-12-07T18:54:48.900759</dc:date>\r\n    <dc:format>image/svg+xml</dc:format>\r\n    <dc:creator>\r\n     <cc:Agent>\r\n      <dc:title>Matplotlib v3.3.2, https://matplotlib.org/</dc:title>\r\n     </cc:Agent>\r\n    </dc:creator>\r\n   </cc:Work>\r\n  </rdf:RDF>\r\n </metadata>\r\n <defs>\r\n  <style type=\"text/css\">*{stroke-linecap:butt;stroke-linejoin:round;}</style>\r\n </defs>\r\n <g id=\"figure_1\">\r\n  <g id=\"patch_1\">\r\n   <path d=\"M 0 263.63625 \r\nL 377.**********.63625 \r\nL 377.449285 0 \r\nL 0 0 \r\nz\r\n\" style=\"fill:none;\"/>\r\n  </g>\r\n  <g id=\"axes_1\">\r\n   <g id=\"patch_2\">\r\n    <path d=\"M 28.**********.758125 \r\nL 363.**********.758125 \r\nL 363.742188 22.318125 \r\nL 28.942188 22.318125 \r\nz\r\n\" style=\"fill:#ffffff;\"/>\r\n   </g>\r\n   <g id=\"matplotlib.axis_1\">\r\n    <g id=\"xtick_1\">\r\n     <g id=\"line2d_1\">\r\n      <defs>\r\n       <path d=\"M 0 0 \r\nL 0 3.5 \r\n\" id=\"m4553b258b0\" style=\"stroke:#000000;stroke-width:0.8;\"/>\r\n      </defs>\r\n      <g>\r\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"44.160369\" xlink:href=\"#m4553b258b0\" y=\"239.758125\"/>\r\n      </g>\r\n     </g>\r\n     <g id=\"text_1\">\r\n      <!-- 2020-01 -->\r\n      <g transform=\"translate(23.268963 254.356563)scale(0.1 -0.1)\">\r\n       <defs>\r\n        <path d=\"M 19.1875 8.296875 \r\nL 53.609375 8.296875 \r\nL 53.609375 0 \r\nL 7.328125 0 \r\nL 7.328125 8.296875 \r\nQ 12.9375 14.109375 22.625 23.890625 \r\nQ 32.328125 33.6875 34.8125 36.53125 \r\nQ 39.546875 41.84375 41.421875 45.53125 \r\nQ 43.3125 49.21875 43.3125 52.78125 \r\nQ 43.3125 58.59375 39.234375 62.25 \r\nQ 35.15625 65.921875 28.609375 65.921875 \r\nQ 23.96875 65.921875 18.8125 64.3125 \r\nQ 13.671875 62.703125 7.8125 59.421875 \r\nL 7.8125 69.390625 \r\nQ 13.765625 71.78125 18.9375 73 \r\nQ 24.125 74.21875 28.421875 74.21875 \r\nQ 39.75 74.21875 46.484375 68.546875 \r\nQ 53.21875 62.890625 53.21875 53.421875 \r\nQ 53.21875 48.921875 51.53125 44.890625 \r\nQ 49.859375 40.875 45.40625 35.40625 \r\nQ 44.1875 33.984375 37.640625 27.21875 \r\nQ 31.109375 20.453125 19.1875 8.296875 \r\nz\r\n\" id=\"DejaVuSans-50\"/>\r\n        <path d=\"M 31.78125 66.40625 \r\nQ 24.171875 66.40625 20.328125 58.90625 \r\nQ 16.5 51.421875 16.5 36.375 \r\nQ 16.5 21.390625 20.328125 13.890625 \r\nQ 24.171875 6.390625 31.78125 6.390625 \r\nQ 39.453125 6.390625 43.28125 13.890625 \r\nQ 47.125 21.390625 47.125 36.375 \r\nQ 47.125 51.421875 43.28125 58.90625 \r\nQ 39.453125 66.40625 31.78125 66.40625 \r\nz\r\nM 31.78125 74.21875 \r\nQ 44.046875 74.21875 50.515625 64.515625 \r\nQ 56.984375 54.828125 56.984375 36.375 \r\nQ 56.984375 17.96875 50.515625 8.265625 \r\nQ 44.046875 -1.421875 31.78125 -1.421875 \r\nQ 19.53125 -1.421875 13.0625 8.265625 \r\nQ 6.59375 17.96875 6.59375 36.375 \r\nQ 6.59375 54.828125 13.0625 64.515625 \r\nQ 19.53125 74.21875 31.78125 74.21875 \r\nz\r\n\" id=\"DejaVuSans-48\"/>\r\n        <path d=\"M 4.890625 31.390625 \r\nL 31.203125 31.390625 \r\nL 31.203125 23.390625 \r\nL 4.890625 23.390625 \r\nz\r\n\" id=\"DejaVuSans-45\"/>\r\n        <path d=\"M 12.40625 8.296875 \r\nL 28.515625 8.296875 \r\nL 28.515625 63.921875 \r\nL 10.984375 60.40625 \r\nL 10.984375 69.390625 \r\nL 28.421875 72.90625 \r\nL 38.28125 72.90625 \r\nL 38.28125 8.296875 \r\nL 54.390625 8.296875 \r\nL 54.390625 0 \r\nL 12.40625 0 \r\nz\r\n\" id=\"DejaVuSans-49\"/>\r\n       </defs>\r\n       <use xlink:href=\"#DejaVuSans-50\"/>\r\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\r\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\r\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\r\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\r\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\r\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-49\"/>\r\n      </g>\r\n     </g>\r\n    </g>\r\n    <g id=\"xtick_2\">\r\n     <g id=\"line2d_2\">\r\n      <g>\r\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"94.192748\" xlink:href=\"#m4553b258b0\" y=\"239.758125\"/>\r\n      </g>\r\n     </g>\r\n     <g id=\"text_2\">\r\n      <!-- 2020-03 -->\r\n      <g transform=\"translate(73.301342 254.356563)scale(0.1 -0.1)\">\r\n       <defs>\r\n        <path d=\"M 40.578125 39.3125 \r\nQ 47.65625 37.796875 51.625 33 \r\nQ 55.609375 28.21875 55.609375 21.1875 \r\nQ 55.609375 10.40625 48.1875 4.484375 \r\nQ 40.765625 -1.421875 27.09375 -1.421875 \r\nQ 22.515625 -1.421875 17.65625 -0.515625 \r\nQ 12.796875 0.390625 7.625 2.203125 \r\nL 7.625 11.71875 \r\nQ 11.71875 9.328125 16.59375 8.109375 \r\nQ 21.484375 6.890625 26.8125 6.890625 \r\nQ 36.078125 6.890625 40.9375 10.546875 \r\nQ 45.796875 14.203125 45.796875 21.1875 \r\nQ 45.796875 27.640625 41.28125 31.265625 \r\nQ 36.765625 34.90625 28.71875 34.90625 \r\nL 20.21875 34.90625 \r\nL 20.21875 43.015625 \r\nL 29.109375 43.015625 \r\nQ 36.375 43.015625 40.234375 45.921875 \r\nQ 44.09375 48.828125 44.09375 54.296875 \r\nQ 44.09375 59.90625 40.109375 62.90625 \r\nQ 36.140625 65.921875 28.71875 65.921875 \r\nQ 24.65625 65.921875 20.015625 65.03125 \r\nQ 15.375 64.15625 9.8125 62.3125 \r\nL 9.8125 71.09375 \r\nQ 15.4375 72.65625 20.34375 73.4375 \r\nQ 25.25 74.21875 29.59375 74.21875 \r\nQ 40.828125 74.21875 47.359375 69.109375 \r\nQ 53.90625 64.015625 53.90625 55.328125 \r\nQ 53.90625 49.265625 50.4375 45.09375 \r\nQ 46.96875 40.921875 40.578125 39.3125 \r\nz\r\n\" id=\"DejaVuSans-51\"/>\r\n       </defs>\r\n       <use xlink:href=\"#DejaVuSans-50\"/>\r\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\r\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\r\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\r\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\r\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\r\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-51\"/>\r\n      </g>\r\n     </g>\r\n    </g>\r\n    <g id=\"xtick_3\">\r\n     <g id=\"line2d_3\">\r\n      <g>\r\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"145.058999\" xlink:href=\"#m4553b258b0\" y=\"239.758125\"/>\r\n      </g>\r\n     </g>\r\n     <g id=\"text_3\">\r\n      <!-- 2020-05 -->\r\n      <g transform=\"translate(124.167593 254.356563)scale(0.1 -0.1)\">\r\n       <defs>\r\n        <path d=\"M 10.796875 72.90625 \r\nL 49.515625 72.90625 \r\nL 49.515625 64.59375 \r\nL 19.828125 64.59375 \r\nL 19.828125 46.734375 \r\nQ 21.96875 47.46875 24.109375 47.828125 \r\nQ 26.265625 48.1875 28.421875 48.1875 \r\nQ 40.625 48.1875 47.75 41.5 \r\nQ 54.890625 34.8125 54.890625 23.390625 \r\nQ 54.890625 11.625 47.5625 5.09375 \r\nQ 40.234375 -1.421875 26.90625 -1.421875 \r\nQ 22.3125 -1.421875 17.546875 -0.640625 \r\nQ 12.796875 0.140625 7.71875 1.703125 \r\nL 7.71875 11.625 \r\nQ 12.109375 9.234375 16.796875 8.0625 \r\nQ 21.484375 6.890625 26.703125 6.890625 \r\nQ 35.15625 6.890625 40.078125 11.328125 \r\nQ 45.015625 15.765625 45.015625 23.390625 \r\nQ 45.015625 31 40.078125 35.4375 \r\nQ 35.15625 39.890625 26.703125 39.890625 \r\nQ 22.75 39.890625 18.8125 39.015625 \r\nQ 14.890625 38.140625 10.796875 36.28125 \r\nz\r\n\" id=\"DejaVuSans-53\"/>\r\n       </defs>\r\n       <use xlink:href=\"#DejaVuSans-50\"/>\r\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\r\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\r\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\r\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\r\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\r\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-53\"/>\r\n      </g>\r\n     </g>\r\n    </g>\r\n    <g id=\"xtick_4\">\r\n     <g id=\"line2d_4\">\r\n      <g>\r\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"195.925251\" xlink:href=\"#m4553b258b0\" y=\"239.758125\"/>\r\n      </g>\r\n     </g>\r\n     <g id=\"text_4\">\r\n      <!-- 2020-07 -->\r\n      <g transform=\"translate(175.033845 254.356563)scale(0.1 -0.1)\">\r\n       <defs>\r\n        <path d=\"M 8.203125 72.90625 \r\nL 55.078125 72.90625 \r\nL 55.078125 68.703125 \r\nL 28.609375 0 \r\nL 18.3125 0 \r\nL 43.21875 64.59375 \r\nL 8.203125 64.59375 \r\nz\r\n\" id=\"DejaVuSans-55\"/>\r\n       </defs>\r\n       <use xlink:href=\"#DejaVuSans-50\"/>\r\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\r\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\r\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\r\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\r\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\r\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-55\"/>\r\n      </g>\r\n     </g>\r\n    </g>\r\n    <g id=\"xtick_5\">\r\n     <g id=\"line2d_5\">\r\n      <g>\r\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"247.625376\" xlink:href=\"#m4553b258b0\" y=\"239.758125\"/>\r\n      </g>\r\n     </g>\r\n     <g id=\"text_5\">\r\n      <!-- 2020-09 -->\r\n      <g transform=\"translate(226.733969 254.356563)scale(0.1 -0.1)\">\r\n       <defs>\r\n        <path d=\"M 10.984375 1.515625 \r\nL 10.984375 10.5 \r\nQ 14.703125 8.734375 18.5 7.8125 \r\nQ 22.3125 6.890625 25.984375 6.890625 \r\nQ 35.75 6.890625 40.890625 13.453125 \r\nQ 46.046875 20.015625 46.78125 33.40625 \r\nQ 43.953125 29.203125 39.59375 26.953125 \r\nQ 35.25 24.703125 29.984375 24.703125 \r\nQ 19.046875 24.703125 12.671875 31.3125 \r\nQ 6.296875 37.9375 6.296875 49.421875 \r\nQ 6.296875 60.640625 12.9375 67.421875 \r\nQ 19.578125 74.21875 30.609375 74.21875 \r\nQ 43.265625 74.21875 49.921875 64.515625 \r\nQ 56.59375 54.828125 56.59375 36.375 \r\nQ 56.59375 19.140625 48.40625 8.859375 \r\nQ 40.234375 -1.421875 26.421875 -1.421875 \r\nQ 22.703125 -1.421875 18.890625 -0.6875 \r\nQ 15.09375 0.046875 10.984375 1.515625 \r\nz\r\nM 30.609375 32.421875 \r\nQ 37.25 32.421875 41.125 36.953125 \r\nQ 45.015625 41.5 45.015625 49.421875 \r\nQ 45.015625 57.28125 41.125 61.84375 \r\nQ 37.25 66.40625 30.609375 66.40625 \r\nQ 23.96875 66.40625 20.09375 61.84375 \r\nQ 16.21875 57.28125 16.21875 49.421875 \r\nQ 16.21875 41.5 20.09375 36.953125 \r\nQ 23.96875 32.421875 30.609375 32.421875 \r\nz\r\n\" id=\"DejaVuSans-57\"/>\r\n       </defs>\r\n       <use xlink:href=\"#DejaVuSans-50\"/>\r\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\r\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\r\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\r\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\r\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\r\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-57\"/>\r\n      </g>\r\n     </g>\r\n    </g>\r\n    <g id=\"xtick_6\">\r\n     <g id=\"line2d_6\">\r\n      <g>\r\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"298.491627\" xlink:href=\"#m4553b258b0\" y=\"239.758125\"/>\r\n      </g>\r\n     </g>\r\n     <g id=\"text_6\">\r\n      <!-- 2020-11 -->\r\n      <g transform=\"translate(277.600221 254.356563)scale(0.1 -0.1)\">\r\n       <use xlink:href=\"#DejaVuSans-50\"/>\r\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\r\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\r\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-48\"/>\r\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\r\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-49\"/>\r\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-49\"/>\r\n      </g>\r\n     </g>\r\n    </g>\r\n    <g id=\"xtick_7\">\r\n     <g id=\"line2d_7\">\r\n      <g>\r\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"349.357879\" xlink:href=\"#m4553b258b0\" y=\"239.758125\"/>\r\n      </g>\r\n     </g>\r\n     <g id=\"text_7\">\r\n      <!-- 2021-01 -->\r\n      <g transform=\"translate(328.466472 254.356563)scale(0.1 -0.1)\">\r\n       <use xlink:href=\"#DejaVuSans-50\"/>\r\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\r\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-50\"/>\r\n       <use x=\"190.869141\" xlink:href=\"#DejaVuSans-49\"/>\r\n       <use x=\"254.492188\" xlink:href=\"#DejaVuSans-45\"/>\r\n       <use x=\"290.576172\" xlink:href=\"#DejaVuSans-48\"/>\r\n       <use x=\"354.199219\" xlink:href=\"#DejaVuSans-49\"/>\r\n      </g>\r\n     </g>\r\n    </g>\r\n   </g>\r\n   <g id=\"matplotlib.axis_2\">\r\n    <g id=\"ytick_1\">\r\n     <g id=\"line2d_8\">\r\n      <defs>\r\n       <path d=\"M 0 0 \r\nL -3.5 0 \r\n\" id=\"md0df6c0ceb\" style=\"stroke:#000000;stroke-width:0.8;\"/>\r\n      </defs>\r\n      <g>\r\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"28.942188\" xlink:href=\"#md0df6c0ceb\" y=\"215.651438\"/>\r\n      </g>\r\n     </g>\r\n     <g id=\"text_8\">\r\n      <!-- −5 -->\r\n      <g transform=\"translate(7.2 219.450657)scale(0.1 -0.1)\">\r\n       <defs>\r\n        <path d=\"M 10.59375 35.5 \r\nL 73.1875 35.5 \r\nL 73.1875 27.203125 \r\nL 10.59375 27.203125 \r\nz\r\n\" id=\"DejaVuSans-8722\"/>\r\n       </defs>\r\n       <use xlink:href=\"#DejaVuSans-8722\"/>\r\n       <use x=\"83.789062\" xlink:href=\"#DejaVuSans-53\"/>\r\n      </g>\r\n     </g>\r\n    </g>\r\n    <g id=\"ytick_2\">\r\n     <g id=\"line2d_9\">\r\n      <g>\r\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"28.942188\" xlink:href=\"#md0df6c0ceb\" y=\"183.118885\"/>\r\n      </g>\r\n     </g>\r\n     <g id=\"text_9\">\r\n      <!-- 0 -->\r\n      <g transform=\"translate(15.579688 186.918104)scale(0.1 -0.1)\">\r\n       <use xlink:href=\"#DejaVuSans-48\"/>\r\n      </g>\r\n     </g>\r\n    </g>\r\n    <g id=\"ytick_3\">\r\n     <g id=\"line2d_10\">\r\n      <g>\r\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"28.942188\" xlink:href=\"#md0df6c0ceb\" y=\"150.586333\"/>\r\n      </g>\r\n     </g>\r\n     <g id=\"text_10\">\r\n      <!-- 5 -->\r\n      <g transform=\"translate(15.579688 154.385552)scale(0.1 -0.1)\">\r\n       <use xlink:href=\"#DejaVuSans-53\"/>\r\n      </g>\r\n     </g>\r\n    </g>\r\n    <g id=\"ytick_4\">\r\n     <g id=\"line2d_11\">\r\n      <g>\r\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"28.942188\" xlink:href=\"#md0df6c0ceb\" y=\"118.05378\"/>\r\n      </g>\r\n     </g>\r\n     <g id=\"text_11\">\r\n      <!-- 10 -->\r\n      <g transform=\"translate(9.217188 121.852999)scale(0.1 -0.1)\">\r\n       <use xlink:href=\"#DejaVuSans-49\"/>\r\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\r\n      </g>\r\n     </g>\r\n    </g>\r\n    <g id=\"ytick_5\">\r\n     <g id=\"line2d_12\">\r\n      <g>\r\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"28.942188\" xlink:href=\"#md0df6c0ceb\" y=\"85.521227\"/>\r\n      </g>\r\n     </g>\r\n     <g id=\"text_12\">\r\n      <!-- 15 -->\r\n      <g transform=\"translate(9.217188 89.320446)scale(0.1 -0.1)\">\r\n       <use xlink:href=\"#DejaVuSans-49\"/>\r\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-53\"/>\r\n      </g>\r\n     </g>\r\n    </g>\r\n    <g id=\"ytick_6\">\r\n     <g id=\"line2d_13\">\r\n      <g>\r\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"28.942188\" xlink:href=\"#md0df6c0ceb\" y=\"52.988675\"/>\r\n      </g>\r\n     </g>\r\n     <g id=\"text_13\">\r\n      <!-- 20 -->\r\n      <g transform=\"translate(9.217188 56.787893)scale(0.1 -0.1)\">\r\n       <use xlink:href=\"#DejaVuSans-50\"/>\r\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\r\n      </g>\r\n     </g>\r\n    </g>\r\n   </g>\r\n   <g id=\"line2d_14\">\r\n    <path clip-path=\"url(#pf9773a3fc8)\" d=\"M 44.160369 189.625396 \r\nL 44.994242 196.131907 \r\nL 45.828115 189.625396 \r\nL 48.329734 189.625396 \r\nL 49.163607 196.131907 \r\nL 49.99748 196.131907 \r\nL 51.665226 209.144928 \r\nL 54.166845 215.651438 \r\nL 55.000718 209.144928 \r\nL 55.834591 209.144928 \r\nL 57.502337 196.131907 \r\nL 60.003956 189.625396 \r\nL 60.837829 189.625396 \r\nL 63.339448 209.144928 \r\nL 65.841067 215.651438 \r\nL 66.67494 209.144928 \r\nL 67.508813 215.651438 \r\nL 71.678178 215.651438 \r\nL 72.512051 209.144928 \r\nL 73.345923 209.144928 \r\nL 74.179796 202.638417 \r\nL 77.515288 202.638417 \r\nL 80.85078 176.612375 \r\nL 83.352399 176.612375 \r\nL 84.186272 183.118885 \r\nL 85.854018 170.105864 \r\nL 86.687891 176.612375 \r\nL 89.18951 170.105864 \r\nL 90.023383 176.612375 \r\nL 90.857256 176.612375 \r\nL 91.691129 183.118885 \r\nL 92.525002 183.118885 \r\nL 95.026621 176.612375 \r\nL 95.860494 170.105864 \r\nL 96.694367 176.612375 \r\nL 98.362113 176.612375 \r\nL 100.863732 170.105864 \r\nL 101.697605 163.599354 \r\nL 102.531478 163.599354 \r\nL 103.365351 170.105864 \r\nL 104.199224 163.599354 \r\nL 106.700843 170.105864 \r\nL 108.368588 157.092843 \r\nL 109.202461 163.599354 \r\nL 110.036334 157.092843 \r\nL 112.537953 157.092843 \r\nL 114.205699 144.079822 \r\nL 115.873445 157.092843 \r\nL 118.375064 150.586333 \r\nL 119.208937 157.092843 \r\nL 121.710556 137.573312 \r\nL 124.212175 144.079822 \r\nL 125.879921 131.066801 \r\nL 126.713794 131.066801 \r\nL 127.547667 137.573312 \r\nL 135.886397 137.573312 \r\nL 136.72027 144.079822 \r\nL 137.554143 144.079822 \r\nL 138.388016 137.573312 \r\nL 139.221889 137.573312 \r\nL 141.723508 144.079822 \r\nL 142.557381 137.573312 \r\nL 143.391254 137.573312 \r\nL 144.225126 131.066801 \r\nL 145.058999 131.066801 \r\nL 147.560618 137.573312 \r\nL 150.062237 157.092843 \r\nL 150.89611 150.586333 \r\nL 153.397729 157.092843 \r\nL 154.231602 157.092843 \r\nL 155.065475 150.586333 \r\nL 155.899348 157.092843 \r\nL 156.733221 150.586333 \r\nL 159.23484 150.586333 \r\nL 160.068713 144.079822 \r\nL 162.570332 144.079822 \r\nL 165.071951 137.573312 \r\nL 166.739697 124.560291 \r\nL 168.407443 137.573312 \r\nL 170.909062 137.573312 \r\nL 171.742935 144.079822 \r\nL 172.576808 137.573312 \r\nL 173.410681 137.573312 \r\nL 174.244554 144.079822 \r\nL 176.746173 137.573312 \r\nL 179.247791 137.573312 \r\nL 180.081664 131.066801 \r\nL 182.583283 124.560291 \r\nL 184.251029 124.560291 \r\nL 185.918775 111.54727 \r\nL 189.254267 111.54727 \r\nL 190.08814 118.05378 \r\nL 191.755886 105.040759 \r\nL 194.257505 105.040759 \r\nL 195.925251 92.027738 \r\nL 196.759124 98.534248 \r\nL 197.592997 98.534248 \r\nL 200.094616 92.027738 \r\nL 200.928489 92.027738 \r\nL 201.762362 98.534248 \r\nL 202.596235 98.534248 \r\nL 203.430108 105.040759 \r\nL 205.931727 105.040759 \r\nL 207.599473 118.05378 \r\nL 208.433346 111.54727 \r\nL 209.267219 118.05378 \r\nL 213.436584 118.05378 \r\nL 214.270456 124.560291 \r\nL 215.104329 124.560291 \r\nL 217.605948 118.05378 \r\nL 218.439821 118.05378 \r\nL 219.273694 111.54727 \r\nL 220.94144 124.560291 \r\nL 223.443059 131.066801 \r\nL 224.276932 124.560291 \r\nL 225.110805 131.066801 \r\nL 225.944678 124.560291 \r\nL 229.28017 124.560291 \r\nL 230.114043 131.066801 \r\nL 230.947916 124.560291 \r\nL 231.781789 131.066801 \r\nL 232.615662 131.066801 \r\nL 235.117281 124.560291 \r\nL 236.785027 137.573312 \r\nL 237.6189 131.066801 \r\nL 238.452773 137.573312 \r\nL 242.622138 137.573312 \r\nL 243.456011 131.066801 \r\nL 244.289884 137.573312 \r\nL 246.791503 137.573312 \r\nL 247.625376 131.066801 \r\nL 248.459249 131.066801 \r\nL 250.126994 118.05378 \r\nL 253.462486 118.05378 \r\nL 254.296359 111.54727 \r\nL 255.130232 118.05378 \r\nL 258.465724 118.05378 \r\nL 260.13347 105.040759 \r\nL 260.967343 105.040759 \r\nL 261.801216 111.54727 \r\nL 264.302835 105.040759 \r\nL 265.136708 98.534248 \r\nL 265.970581 98.534248 \r\nL 266.804454 105.040759 \r\nL 267.638327 105.040759 \r\nL 270.139946 111.54727 \r\nL 270.973819 111.54727 \r\nL 273.475438 131.066801 \r\nL 275.977057 124.560291 \r\nL 277.644803 124.560291 \r\nL 278.478676 118.05378 \r\nL 279.312549 124.560291 \r\nL 281.814168 131.066801 \r\nL 282.648041 131.066801 \r\nL 283.481914 124.560291 \r\nL 285.149659 137.573312 \r\nL 287.651278 137.573312 \r\nL 289.319024 124.560291 \r\nL 293.488389 124.560291 \r\nL 294.322262 118.05378 \r\nL 295.156135 118.05378 \r\nL 295.990008 111.54727 \r\nL 296.823881 118.05378 \r\nL 299.3255 118.05378 \r\nL 302.660992 92.027738 \r\nL 305.996484 92.027738 \r\nL 306.830357 85.521227 \r\nL 307.66423 92.027738 \r\nL 308.498103 85.521227 \r\nL 310.999722 85.521227 \r\nL 311.833595 92.027738 \r\nL 312.667468 92.027738 \r\nL 314.335214 79.014717 \r\nL 316.836833 72.508206 \r\nL 318.504579 72.508206 \r\nL 320.172324 59.495185 \r\nL 322.673943 52.988675 \r\nL 323.507816 52.988675 \r\nL 324.341689 46.482164 \r\nL 325.175562 52.988675 \r\nL 326.009435 52.988675 \r\nL 328.511054 59.495185 \r\nL 329.344927 52.988675 \r\nL 330.1788 52.988675 \r\nL 331.012673 46.482164 \r\nL 331.846546 46.482164 \r\nL 334.348165 52.988675 \r\nL 335.182038 52.988675 \r\nL 336.015911 46.482164 \r\nL 337.683657 46.482164 \r\nL 340.185276 52.988675 \r\nL 341.019149 52.988675 \r\nL 343.520768 72.508206 \r\nL 346.022387 66.001696 \r\nL 346.85626 59.495185 \r\nL 347.690133 59.495185 \r\nL 348.524006 66.001696 \r\nL 348.524006 66.001696 \r\n\" style=\"fill:none;stroke:#1f77b4;stroke-linecap:square;stroke-width:1.5;\"/>\r\n   </g>\r\n   <g id=\"line2d_15\">\r\n    <path clip-path=\"url(#pf9773a3fc8)\" d=\"M 44.160369 189.625396 \r\nL 44.994242 192.878651 \r\nL 45.828115 191.794233 \r\nL 48.329734 191.252024 \r\nL 49.163607 192.228 \r\nL 49.99748 192.878651 \r\nL 50.831353 194.272904 \r\nL 51.665226 196.131907 \r\nL 54.166845 198.300743 \r\nL 55.000718 199.385162 \r\nL 55.834591 200.272413 \r\nL 56.668464 200.46958 \r\nL 57.502337 200.135913 \r\nL 60.003956 199.385162 \r\nL 60.837829 198.734511 \r\nL 61.671702 198.571848 \r\nL 62.505575 198.811058 \r\nL 63.339448 199.385162 \r\nL 65.841067 200.241282 \r\nL 66.67494 200.686464 \r\nL 68.342686 202.046916 \r\nL 69.176559 203.229918 \r\nL 71.678178 204.807254 \r\nL 72.512051 205.004421 \r\nL 73.345923 205.891672 \r\nL 75.013669 206.483173 \r\nL 77.515288 206.356423 \r\nL 78.349161 205.891672 \r\nL 80.016907 203.525669 \r\nL 80.85078 202.046916 \r\nL 83.352399 201.089248 \r\nL 84.186272 200.272413 \r\nL 85.020145 199.680912 \r\nL 86.687891 197.906409 \r\nL 89.18951 195.822073 \r\nL 90.023383 194.948905 \r\nL 90.857256 193.174402 \r\nL 91.691129 191.9914 \r\nL 92.525002 190.512647 \r\nL 95.026621 187.456559 \r\nL 95.860494 186.667891 \r\nL 96.694367 184.893388 \r\nL 98.362113 181.935884 \r\nL 101.697605 178.682628 \r\nL 102.531478 176.908125 \r\nL 104.199224 174.542122 \r\nL 106.700843 173.82387 \r\nL 107.534716 173.35912 \r\nL 110.036334 170.697365 \r\nL 112.537953 169.796031 \r\nL 113.371826 168.922862 \r\nL 115.039572 166.556859 \r\nL 115.873445 165.669607 \r\nL 118.375064 163.28952 \r\nL 119.208937 163.007853 \r\nL 120.876683 160.641849 \r\nL 121.710556 158.867346 \r\nL 125.046048 155.614091 \r\nL 125.879921 153.839588 \r\nL 126.713794 152.360836 \r\nL 127.547667 151.177834 \r\nL 130.049286 149.037164 \r\nL 130.883159 148.516079 \r\nL 131.717032 147.037327 \r\nL 132.550905 145.854325 \r\nL 133.384778 144.967074 \r\nL 135.886397 143.150321 \r\nL 136.72027 143.192571 \r\nL 138.388016 142.009569 \r\nL 139.221889 141.713818 \r\nL 141.723508 140.67165 \r\nL 142.557381 140.530816 \r\nL 143.391254 139.939316 \r\nL 144.225126 138.756314 \r\nL 145.058999 137.869062 \r\nL 147.560618 137.573312 \r\nL 149.228364 138.164813 \r\nL 150.89611 139.939316 \r\nL 153.397729 141.291318 \r\nL 156.733221 144.079822 \r\nL 159.23484 145.009324 \r\nL 160.068713 144.967074 \r\nL 160.902586 145.262824 \r\nL 162.570332 145.262824 \r\nL 165.071951 145.628991 \r\nL 165.905824 144.967074 \r\nL 166.739697 144.079822 \r\nL 167.57357 143.784072 \r\nL 168.407443 143.784072 \r\nL 170.909062 144.69949 \r\nL 172.576808 144.671323 \r\nL 174.244554 144.079822 \r\nL 176.746173 142.840487 \r\nL 177.580046 142.60107 \r\nL 180.081664 139.939316 \r\nL 183.417156 137.277561 \r\nL 185.084902 134.911557 \r\nL 185.918775 133.432805 \r\nL 188.420394 131.376635 \r\nL 191.755886 127.813546 \r\nL 194.257505 126.10946 \r\nL 195.091378 124.856041 \r\nL 197.592997 118.941032 \r\nL 200.928489 114.504774 \r\nL 202.596235 110.955769 \r\nL 203.430108 109.477016 \r\nL 205.931727 106.899762 \r\nL 206.7656 107.111012 \r\nL 207.599473 106.815262 \r\nL 208.433346 106.223761 \r\nL 209.267219 105.92801 \r\nL 211.768838 105.660427 \r\nL 212.602711 106.223761 \r\nL 213.436584 106.519511 \r\nL 214.270456 107.111012 \r\nL 215.104329 107.406763 \r\nL 217.605948 107.829264 \r\nL 218.439821 108.294014 \r\nL 219.273694 108.589765 \r\nL 220.107567 109.477016 \r\nL 220.94144 110.955769 \r\nL 223.443059 113.096439 \r\nL 224.276932 113.617523 \r\nL 225.110805 115.392026 \r\nL 225.944678 116.870778 \r\nL 226.778551 118.05378 \r\nL 230.114043 120.419784 \r\nL 231.781789 122.194287 \r\nL 232.615662 122.785788 \r\nL 235.951154 123.96879 \r\nL 236.785027 124.856041 \r\nL 237.6189 125.447542 \r\nL 238.452773 126.334793 \r\nL 240.954392 127.038961 \r\nL 241.788265 127.517795 \r\nL 242.622138 128.405047 \r\nL 243.456011 128.996548 \r\nL 244.289884 130.17955 \r\nL 246.791503 131.376635 \r\nL 249.293121 131.362552 \r\nL 250.126994 130.771051 \r\nL 252.628613 130.756967 \r\nL 255.130232 128.996548 \r\nL 255.964105 128.700797 \r\nL 258.465724 127.968463 \r\nL 260.13347 126.334793 \r\nL 261.801216 123.96879 \r\nL 264.302835 122.08162 \r\nL 265.136708 121.011285 \r\nL 265.970581 119.236782 \r\nL 267.638327 116.279277 \r\nL 270.139946 114.335774 \r\nL 270.973819 114.209024 \r\nL 271.807692 113.321772 \r\nL 272.641565 113.026022 \r\nL 273.475438 113.026022 \r\nL 275.977057 112.786605 \r\nL 276.81093 113.321772 \r\nL 277.644803 113.617523 \r\nL 278.478676 113.617523 \r\nL 279.312549 114.209024 \r\nL 281.814168 114.645608 \r\nL 282.648041 115.392026 \r\nL 283.481914 115.687776 \r\nL 284.315787 116.575028 \r\nL 285.149659 118.05378 \r\nL 288.485151 120.419784 \r\nL 289.319024 121.307035 \r\nL 290.98677 123.673039 \r\nL 293.488389 125.489792 \r\nL 294.322262 125.151792 \r\nL 295.156135 125.447542 \r\nL 296.823881 125.447542 \r\nL 299.3255 124.870124 \r\nL 300.159373 124.26454 \r\nL 300.993246 123.377289 \r\nL 301.827119 122.194287 \r\nL 302.660992 120.715534 \r\nL 305.162611 119.293115 \r\nL 305.996484 118.05378 \r\nL 307.66423 114.209024 \r\nL 308.498103 112.434521 \r\nL 310.999722 109.068599 \r\nL 311.833595 108.294014 \r\nL 314.335214 102.083254 \r\nL 317.670706 97.351247 \r\nL 320.172324 89.957485 \r\nL 323.507816 84.338225 \r\nL 324.341689 81.08497 \r\nL 326.009435 76.057212 \r\nL 329.344927 72.508206 \r\nL 330.1788 70.733703 \r\nL 331.846546 66.888947 \r\nL 334.348165 64.142693 \r\nL 335.182038 63.635692 \r\nL 336.015911 61.861189 \r\nL 337.683657 57.720682 \r\nL 340.185276 55.157512 \r\nL 341.019149 55.058928 \r\nL 341.853022 54.467427 \r\nL 342.686895 54.171677 \r\nL 343.520768 54.171677 \r\nL 346.022387 53.918176 \r\nL 347.690133 54.467427 \r\nL 348.524006 55.058928 \r\nL 348.524006 55.058928 \r\n\" style=\"fill:none;stroke:#ff7f0e;stroke-linecap:square;stroke-width:1.5;\"/>\r\n   </g>\r\n   <g id=\"line2d_16\">\r\n    <path clip-path=\"url(#pf9773a3fc8)\" d=\"M 44.994242 183.677056 \r\nL 45.828115 184.281162 \r\nL 48.329734 184.745513 \r\nL 49.163607 185.100475 \r\nL 49.99748 185.751126 \r\nL 50.831353 184.435984 \r\nL 51.665226 182.220402 \r\nL 54.166845 179.897553 \r\nL 55.834591 181.619958 \r\nL 56.668464 182.632778 \r\nL 57.502337 182.889804 \r\nL 60.003956 181.889097 \r\nL 60.837829 181.137695 \r\nL 61.671702 181.521976 \r\nL 62.505575 182.185158 \r\nL 63.339448 182.536085 \r\nL 65.841067 182.246194 \r\nL 66.67494 182.724426 \r\nL 67.508813 182.71325 \r\nL 68.342686 182.82541 \r\nL 69.176559 184.008412 \r\nL 72.512051 186.36376 \r\nL 73.345923 188.502292 \r\nL 75.013669 190.092034 \r\nL 77.515288 189.604753 \r\nL 78.349161 188.972362 \r\nL 79.183034 187.026659 \r\nL 80.016907 183.731102 \r\nL 80.85078 179.361816 \r\nL 84.186272 174.077916 \r\nL 85.020145 171.937058 \r\nL 85.854018 168.565043 \r\nL 86.687891 166.238806 \r\nL 89.18951 161.769657 \r\nL 90.023383 160.722559 \r\nL 90.857256 159.400834 \r\nL 91.691129 158.742842 \r\nL 95.026621 159.145138 \r\nL 95.860494 158.065375 \r\nL 96.694367 159.122093 \r\nL 97.52824 159.834303 \r\nL 98.362113 161.209623 \r\nL 100.863732 162.64921 \r\nL 101.697605 161.000617 \r\nL 102.531478 161.628002 \r\nL 103.365351 162.839476 \r\nL 104.199224 162.238523 \r\nL 106.700843 161.776154 \r\nL 107.534716 160.819459 \r\nL 108.368588 158.247706 \r\nL 109.202461 157.71269 \r\nL 110.036334 155.720089 \r\nL 112.537953 153.607398 \r\nL 113.371826 151.127206 \r\nL 114.205699 147.048999 \r\nL 115.039572 145.032477 \r\nL 115.873445 144.273323 \r\nL 118.375064 143.780319 \r\nL 119.208937 143.786347 \r\nL 120.04281 142.910909 \r\nL 120.876683 140.672147 \r\nL 121.710556 137.929947 \r\nL 124.212175 137.658326 \r\nL 125.046048 135.561963 \r\nL 126.713794 129.22775 \r\nL 127.547667 127.792609 \r\nL 130.049286 126.913205 \r\nL 130.883159 126.378871 \r\nL 131.717032 126.667827 \r\nL 132.550905 126.518744 \r\nL 133.384778 126.004746 \r\nL 135.886397 126.54369 \r\nL 136.72027 126.981313 \r\nL 137.554143 127.611562 \r\nL 139.221889 126.93362 \r\nL 141.723508 127.908903 \r\nL 143.391254 128.199776 \r\nL 144.225126 129.222142 \r\nL 145.058999 129.37158 \r\nL 148.394491 129.37158 \r\nL 149.228364 128.4027 \r\nL 150.062237 126.396023 \r\nL 150.89611 126.898158 \r\nL 153.397729 126.700809 \r\nL 154.231602 126.256997 \r\nL 155.065475 126.571711 \r\nL 155.899348 126.493357 \r\nL 156.733221 127.041778 \r\nL 160.068713 128.265805 \r\nL 160.902586 128.88287 \r\nL 162.570332 128.88287 \r\nL 165.071951 129.217739 \r\nL 165.905824 127.789766 \r\nL 166.739697 124.820216 \r\nL 167.57357 123.915576 \r\nL 168.407443 123.915576 \r\nL 170.909062 125.884641 \r\nL 171.742935 126.308009 \r\nL 172.576808 126.308009 \r\nL 173.410681 125.76443 \r\nL 174.244554 125.676631 \r\nL 176.746173 125.084007 \r\nL 177.580046 125.12758 \r\nL 178.413919 125.378679 \r\nL 179.247791 125.935191 \r\nL 180.081664 125.159117 \r\nL 182.583283 124.563894 \r\nL 183.417156 123.092105 \r\nL 184.251029 122.222632 \r\nL 185.084902 119.535783 \r\nL 185.918775 115.67839 \r\nL 188.420394 112.306369 \r\nL 189.254267 110.03395 \r\nL 190.08814 108.747156 \r\nL 190.922013 106.505714 \r\nL 191.755886 103.468413 \r\nL 194.257505 99.790721 \r\nL 195.091378 96.608129 \r\nL 195.925251 91.891539 \r\nL 197.592997 87.49994 \r\nL 200.094616 84.925041 \r\nL 200.928489 82.953128 \r\nL 201.762362 82.242072 \r\nL 202.596235 82.021987 \r\nL 203.430108 83.024919 \r\nL 205.931727 85.460693 \r\nL 207.599473 86.66285 \r\nL 209.267219 88.287506 \r\nL 211.768838 87.768227 \r\nL 212.602711 87.980601 \r\nL 213.436584 87.712456 \r\nL 214.270456 86.87692 \r\nL 215.104329 86.329779 \r\nL 217.605948 85.833243 \r\nL 218.439821 86.389836 \r\nL 219.273694 86.693955 \r\nL 220.107567 87.707117 \r\nL 220.94144 89.740119 \r\nL 224.276932 91.847624 \r\nL 225.110805 94.656925 \r\nL 225.944678 98.627618 \r\nL 226.778551 101.495778 \r\nL 229.28017 106.807141 \r\nL 230.114043 106.774298 \r\nL 230.947916 109.427827 \r\nL 231.781789 110.454747 \r\nL 232.615662 110.616986 \r\nL 235.117281 112.523343 \r\nL 235.951154 112.674816 \r\nL 236.785027 112.493004 \r\nL 237.6189 113.20367 \r\nL 238.452773 113.520456 \r\nL 240.954392 113.098504 \r\nL 241.788265 113.190931 \r\nL 242.622138 114.116609 \r\nL 243.456011 115.445402 \r\nL 244.289884 118.613007 \r\nL 246.791503 120.904435 \r\nL 247.625376 121.141877 \r\nL 249.293121 121.141877 \r\nL 250.126994 119.078438 \r\nL 252.628613 118.089347 \r\nL 253.462486 116.682609 \r\nL 254.296359 114.069903 \r\nL 255.130232 112.740133 \r\nL 255.964105 111.879256 \r\nL 258.465724 110.211983 \r\nL 259.299597 108.53229 \r\nL 260.967343 102.466692 \r\nL 261.801216 101.28369 \r\nL 264.302835 98.630657 \r\nL 265.970581 93.636727 \r\nL 266.804454 92.847347 \r\nL 267.638327 92.45923 \r\nL 270.139946 93.12358 \r\nL 270.973819 93.473923 \r\nL 271.807692 95.280647 \r\nL 272.641565 96.020278 \r\nL 273.475438 96.020278 \r\nL 275.977057 95.513542 \r\nL 276.81093 95.73329 \r\nL 277.644803 95.485203 \r\nL 279.312549 95.519269 \r\nL 281.814168 94.227582 \r\nL 283.481914 94.231618 \r\nL 284.315787 94.240015 \r\nL 285.149659 94.637228 \r\nL 287.651278 95.518017 \r\nL 288.485151 96.142496 \r\nL 289.319024 97.976733 \r\nL 290.152897 101.474008 \r\nL 290.98677 105.581193 \r\nL 293.488389 111.665508 \r\nL 294.322262 111.293082 \r\nL 295.156135 112.561895 \r\nL 296.823881 112.561895 \r\nL 299.3255 111.550873 \r\nL 300.159373 110.079084 \r\nL 300.993246 106.997334 \r\nL 301.827119 102.707644 \r\nL 302.660992 97.416674 \r\nL 305.162611 92.428539 \r\nL 305.996484 89.374478 \r\nL 306.830357 84.776462 \r\nL 307.66423 82.16746 \r\nL 308.498103 78.525566 \r\nL 311.833595 74.995885 \r\nL 312.667468 74.952164 \r\nL 313.501341 73.755609 \r\nL 314.335214 71.307876 \r\nL 316.836833 68.29472 \r\nL 317.670706 65.822842 \r\nL 318.504579 64.209864 \r\nL 319.338452 61.277395 \r\nL 320.172324 57.733412 \r\nL 322.673943 53.566066 \r\nL 324.341689 46.448777 \r\nL 326.009435 42.764587 \r\nL 328.511054 41.222736 \r\nL 329.344927 39.882836 \r\nL 330.1788 38.311187 \r\nL 331.846546 33.944946 \r\nL 335.182038 33.171547 \r\nL 336.015911 32.201761 \r\nL 336.849784 32.708938 \r\nL 337.683657 34.241603 \r\nL 340.185276 37.537798 \r\nL 341.019149 37.838997 \r\nL 342.686895 39.896071 \r\nL 343.520768 39.896071 \r\nL 346.022387 39.494421 \r\nL 346.85626 39.896071 \r\nL 347.690133 40.025895 \r\nL 348.524006 39.826855 \r\nL 348.524006 39.826855 \r\n\" style=\"fill:none;stroke:#2ca02c;stroke-linecap:square;stroke-width:1.5;\"/>\r\n   </g>\r\n   <g id=\"line2d_17\">\r\n    <path clip-path=\"url(#pf9773a3fc8)\" d=\"M 44.994242 202.080247 \r\nL 45.828115 199.307304 \r\nL 48.329734 197.758534 \r\nL 49.163607 199.355525 \r\nL 49.99748 200.006176 \r\nL 50.831353 204.109823 \r\nL 51.665226 210.043411 \r\nL 54.166845 216.703934 \r\nL 55.000718 218.042202 \r\nL 55.834591 218.924869 \r\nL 56.668464 218.306383 \r\nL 57.502337 217.382022 \r\nL 60.003956 216.881227 \r\nL 60.837829 216.331327 \r\nL 61.671702 215.62172 \r\nL 62.505575 215.436958 \r\nL 63.339448 216.234238 \r\nL 65.841067 218.236369 \r\nL 66.67494 218.648502 \r\nL 67.508813 220.084914 \r\nL 69.176559 222.451424 \r\nL 71.678178 223.813988 \r\nL 72.512051 223.645082 \r\nL 73.345923 223.281053 \r\nL 75.013669 222.874312 \r\nL 77.515288 223.108093 \r\nL 78.349161 222.810983 \r\nL 79.183034 222.390682 \r\nL 80.016907 223.320235 \r\nL 80.85078 224.732016 \r\nL 83.352399 226.756508 \r\nL 84.186272 226.466911 \r\nL 85.020145 227.424766 \r\nL 85.854018 229.022278 \r\nL 86.687891 229.574012 \r\nL 89.18951 229.874489 \r\nL 90.023383 229.17525 \r\nL 90.857256 226.947969 \r\nL 91.691129 225.239958 \r\nL 92.525002 222.209173 \r\nL 95.026621 215.767981 \r\nL 95.860494 215.270407 \r\nL 96.694367 210.664683 \r\nL 97.52824 206.994969 \r\nL 98.362113 202.662144 \r\nL 100.863732 196.152549 \r\nL 101.697605 196.364639 \r\nL 102.531478 192.188249 \r\nL 103.365351 188.610771 \r\nL 104.199224 186.845721 \r\nL 106.700843 185.871587 \r\nL 107.534716 185.898781 \r\nL 108.368588 186.69603 \r\nL 109.202461 185.456544 \r\nL 110.036334 185.674642 \r\nL 112.537953 185.984663 \r\nL 113.371826 186.718519 \r\nL 114.205699 188.430722 \r\nL 115.039572 188.08124 \r\nL 115.873445 187.065892 \r\nL 118.375064 182.798721 \r\nL 119.208937 182.229359 \r\nL 120.04281 180.738793 \r\nL 120.876683 180.611551 \r\nL 121.710556 179.804745 \r\nL 124.212175 175.288025 \r\nL 125.046048 175.666219 \r\nL 125.879921 175.372482 \r\nL 126.713794 175.493922 \r\nL 127.547667 174.563059 \r\nL 130.049286 171.161122 \r\nL 130.883159 170.653288 \r\nL 131.717032 167.406827 \r\nL 132.550905 165.189906 \r\nL 133.384778 163.929401 \r\nL 135.886397 159.756952 \r\nL 136.72027 159.403829 \r\nL 137.554143 157.590578 \r\nL 138.388016 156.702715 \r\nL 139.221889 156.494017 \r\nL 141.723508 153.434397 \r\nL 142.557381 153.055854 \r\nL 143.391254 151.678855 \r\nL 144.225126 148.290486 \r\nL 145.058999 146.366544 \r\nL 147.560618 145.803469 \r\nL 148.394491 146.366544 \r\nL 149.228364 147.926925 \r\nL 150.062237 151.708105 \r\nL 150.89611 152.980473 \r\nL 153.397729 155.881827 \r\nL 154.231602 157.762141 \r\nL 155.065475 158.630429 \r\nL 155.899348 160.483285 \r\nL 156.733221 161.117866 \r\nL 159.23484 162.118212 \r\nL 160.068713 161.668343 \r\nL 162.570332 161.642778 \r\nL 165.905824 162.144381 \r\nL 166.739697 163.339428 \r\nL 167.57357 163.652568 \r\nL 168.407443 163.652568 \r\nL 170.909062 163.514339 \r\nL 171.742935 163.034637 \r\nL 173.410681 162.986716 \r\nL 174.244554 162.483013 \r\nL 177.580046 160.074559 \r\nL 178.413919 158.048957 \r\nL 179.247791 155.717943 \r\nL 180.081664 154.719514 \r\nL 182.583283 151.202397 \r\nL 183.417156 151.463018 \r\nL 184.251029 149.966486 \r\nL 185.084902 150.287332 \r\nL 185.918775 151.18722 \r\nL 188.420394 150.446901 \r\nL 189.254267 150.91665 \r\nL 190.08814 150.428942 \r\nL 190.922013 150.895881 \r\nL 191.755886 152.158679 \r\nL 194.257505 152.428198 \r\nL 195.925251 153.680036 \r\nL 196.759124 151.734106 \r\nL 200.094616 146.225178 \r\nL 200.928489 146.056421 \r\nL 201.762362 143.21847 \r\nL 202.596235 139.88955 \r\nL 203.430108 135.929113 \r\nL 205.931727 128.338831 \r\nL 206.7656 128.127042 \r\nL 207.599473 126.967674 \r\nL 208.433346 124.903707 \r\nL 209.267219 123.568515 \r\nL 211.768838 123.552626 \r\nL 213.436584 125.326567 \r\nL 214.270456 127.345105 \r\nL 215.104329 128.483746 \r\nL 218.439821 130.198193 \r\nL 219.273694 130.485575 \r\nL 220.107567 131.246915 \r\nL 223.443059 134.834324 \r\nL 224.276932 135.387422 \r\nL 225.110805 136.127127 \r\nL 225.944678 135.113938 \r\nL 229.28017 133.018425 \r\nL 230.114043 134.06527 \r\nL 230.947916 133.186244 \r\nL 231.781789 133.933826 \r\nL 232.615662 134.95459 \r\nL 235.117281 134.738235 \r\nL 235.951154 135.262763 \r\nL 236.785027 137.219078 \r\nL 237.6189 137.691415 \r\nL 238.452773 139.149131 \r\nL 240.954392 140.979418 \r\nL 242.622138 142.693484 \r\nL 243.456011 142.547693 \r\nL 244.289884 141.746093 \r\nL 246.791503 141.848835 \r\nL 247.625376 141.583227 \r\nL 249.293121 141.583227 \r\nL 250.126994 142.463663 \r\nL 253.462486 143.67649 \r\nL 254.296359 145.106195 \r\nL 255.130232 145.252963 \r\nL 255.964105 145.522339 \r\nL 258.465724 145.724942 \r\nL 259.299597 145.911799 \r\nL 260.13347 147.272193 \r\nL 260.967343 147.836892 \r\nL 261.801216 146.65389 \r\nL 264.302835 145.532582 \r\nL 265.136708 146.002763 \r\nL 265.970581 144.836837 \r\nL 266.804454 142.668712 \r\nL 267.638327 140.099325 \r\nL 270.139946 135.547968 \r\nL 270.973819 134.944125 \r\nL 271.807692 131.362898 \r\nL 272.641565 130.031766 \r\nL 275.977057 130.059668 \r\nL 277.644803 131.749843 \r\nL 278.478676 131.749843 \r\nL 279.312549 132.898778 \r\nL 281.814168 135.063634 \r\nL 282.648041 136.51244 \r\nL 283.481914 137.143935 \r\nL 284.315787 138.910041 \r\nL 285.149659 141.470332 \r\nL 287.651278 144.30755 \r\nL 288.485151 144.697072 \r\nL 289.319024 144.637338 \r\nL 290.152897 143.506067 \r\nL 290.98677 141.764885 \r\nL 293.488389 139.314077 \r\nL 294.322262 139.010501 \r\nL 295.156135 138.33319 \r\nL 296.823881 138.33319 \r\nL 299.3255 138.189376 \r\nL 300.159373 138.449997 \r\nL 300.993246 139.757243 \r\nL 301.827119 141.68093 \r\nL 302.660992 144.014395 \r\nL 305.996484 146.733082 \r\nL 306.830357 147.190592 \r\nL 307.66423 146.250587 \r\nL 308.498103 146.343476 \r\nL 310.999722 142.366728 \r\nL 311.833595 141.592144 \r\nL 312.667468 137.495358 \r\nL 313.501341 134.551407 \r\nL 314.335214 132.858633 \r\nL 316.836833 128.773777 \r\nL 317.670706 128.879651 \r\nL 318.504579 125.760621 \r\nL 320.172324 122.181558 \r\nL 322.673943 118.096057 \r\nL 323.507816 118.799366 \r\nL 326.009435 109.349837 \r\nL 328.511054 105.65268 \r\nL 329.344927 105.133577 \r\nL 331.012673 101.204452 \r\nL 331.846546 99.832949 \r\nL 334.348165 94.976375 \r\nL 335.182038 94.099837 \r\nL 336.015911 91.520617 \r\nL 336.849784 86.872933 \r\nL 337.683657 81.199762 \r\nL 340.185276 72.777225 \r\nL 341.019149 72.278859 \r\nL 341.853022 69.985573 \r\nL 342.686895 68.447282 \r\nL 346.85626 68.447282 \r\nL 347.690133 68.908959 \r\nL 348.524006 70.291001 \r\nL 348.524006 70.291001 \r\n\" style=\"fill:none;stroke:#d62728;stroke-linecap:square;stroke-width:1.5;\"/>\r\n   </g>\r\n   <g id=\"patch_3\">\r\n    <path d=\"M 28.**********.758125 \r\nL 28.942188 22.318125 \r\n\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;\"/>\r\n   </g>\r\n   <g id=\"patch_4\">\r\n    <path d=\"M 363.**********.758125 \r\nL 363.742188 22.318125 \r\n\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;\"/>\r\n   </g>\r\n   <g id=\"patch_5\">\r\n    <path d=\"M 28.**********.758125 \r\nL 363.**********.758125 \r\n\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;\"/>\r\n   </g>\r\n   <g id=\"patch_6\">\r\n    <path d=\"M 28.942188 22.318125 \r\nL 363.742188 22.318125 \r\n\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;\"/>\r\n   </g>\r\n   <g id=\"text_14\">\r\n    <!-- BOLL LINES -->\r\n    <g transform=\"translate(161.812188 16.318125)scale(0.12 -0.12)\">\r\n     <defs>\r\n      <path d=\"M 19.671875 34.8125 \r\nL 19.671875 8.109375 \r\nL 35.5 8.109375 \r\nQ 43.453125 8.109375 47.28125 11.40625 \r\nQ 51.125 14.703125 51.125 21.484375 \r\nQ 51.125 28.328125 47.28125 31.5625 \r\nQ 43.453125 34.8125 35.5 34.8125 \r\nz\r\nM 19.671875 64.796875 \r\nL 19.671875 42.828125 \r\nL 34.28125 42.828125 \r\nQ 41.5 42.828125 45.03125 45.53125 \r\nQ 48.578125 48.25 48.578125 53.8125 \r\nQ 48.578125 59.328125 45.03125 62.0625 \r\nQ 41.5 64.796875 34.28125 64.796875 \r\nz\r\nM 9.8125 72.90625 \r\nL 35.015625 72.90625 \r\nQ 46.296875 72.90625 52.390625 68.21875 \r\nQ 58.5 63.53125 58.5 54.890625 \r\nQ 58.5 48.1875 55.375 44.234375 \r\nQ 52.25 40.28125 46.1875 39.3125 \r\nQ 53.46875 37.75 57.5 32.78125 \r\nQ 61.53125 27.828125 61.53125 20.40625 \r\nQ 61.53125 10.640625 54.890625 5.3125 \r\nQ 48.25 0 35.984375 0 \r\nL 9.8125 0 \r\nz\r\n\" id=\"DejaVuSans-66\"/>\r\n      <path d=\"M 39.40625 66.21875 \r\nQ 28.65625 66.21875 22.328125 58.203125 \r\nQ 16.015625 50.203125 16.015625 36.375 \r\nQ 16.015625 22.609375 22.328125 14.59375 \r\nQ 28.65625 6.59375 39.40625 6.59375 \r\nQ 50.140625 6.59375 56.421875 14.59375 \r\nQ 62.703125 22.609375 62.703125 36.375 \r\nQ 62.703125 50.203125 56.421875 58.203125 \r\nQ 50.140625 66.21875 39.40625 66.21875 \r\nz\r\nM 39.40625 74.21875 \r\nQ 54.734375 74.21875 63.90625 63.9375 \r\nQ 73.09375 53.65625 73.09375 36.375 \r\nQ 73.09375 19.140625 63.90625 8.859375 \r\nQ 54.734375 -1.421875 39.40625 -1.421875 \r\nQ 24.03125 -1.421875 14.8125 8.828125 \r\nQ 5.609375 19.09375 5.609375 36.375 \r\nQ 5.609375 53.65625 14.8125 63.9375 \r\nQ 24.03125 74.21875 39.40625 74.21875 \r\nz\r\n\" id=\"DejaVuSans-79\"/>\r\n      <path d=\"M 9.8125 72.90625 \r\nL 19.671875 72.90625 \r\nL 19.671875 8.296875 \r\nL 55.171875 8.296875 \r\nL 55.171875 0 \r\nL 9.8125 0 \r\nz\r\n\" id=\"DejaVuSans-76\"/>\r\n      <path id=\"DejaVuSans-32\"/>\r\n      <path d=\"M 9.8125 72.90625 \r\nL 19.671875 72.90625 \r\nL 19.671875 0 \r\nL 9.8125 0 \r\nz\r\n\" id=\"DejaVuSans-73\"/>\r\n      <path d=\"M 9.8125 72.90625 \r\nL 23.09375 72.90625 \r\nL 55.421875 11.921875 \r\nL 55.421875 72.90625 \r\nL 64.984375 72.90625 \r\nL 64.984375 0 \r\nL 51.703125 0 \r\nL 19.390625 60.984375 \r\nL 19.390625 0 \r\nL 9.8125 0 \r\nz\r\n\" id=\"DejaVuSans-78\"/>\r\n      <path d=\"M 9.8125 72.90625 \r\nL 55.90625 72.90625 \r\nL 55.90625 64.59375 \r\nL 19.671875 64.59375 \r\nL 19.671875 43.015625 \r\nL 54.390625 43.015625 \r\nL 54.390625 34.71875 \r\nL 19.671875 34.71875 \r\nL 19.671875 8.296875 \r\nL 56.78125 8.296875 \r\nL 56.78125 0 \r\nL 9.8125 0 \r\nz\r\n\" id=\"DejaVuSans-69\"/>\r\n      <path d=\"M 53.515625 70.515625 \r\nL 53.515625 60.890625 \r\nQ 47.90625 63.578125 42.921875 64.890625 \r\nQ 37.9375 66.21875 33.296875 66.21875 \r\nQ 25.25 66.21875 20.875 63.09375 \r\nQ 16.5 59.96875 16.5 54.203125 \r\nQ 16.5 49.359375 19.40625 46.890625 \r\nQ 22.3125 44.4375 30.421875 42.921875 \r\nL 36.375 41.703125 \r\nQ 47.40625 39.59375 52.65625 34.296875 \r\nQ 57.90625 29 57.90625 20.125 \r\nQ 57.90625 9.515625 50.796875 4.046875 \r\nQ 43.703125 -1.421875 29.984375 -1.421875 \r\nQ 24.8125 -1.421875 18.96875 -0.25 \r\nQ 13.140625 0.921875 6.890625 3.21875 \r\nL 6.890625 13.375 \r\nQ 12.890625 10.015625 18.65625 8.296875 \r\nQ 24.421875 6.59375 29.984375 6.59375 \r\nQ 38.421875 6.59375 43.015625 9.90625 \r\nQ 47.609375 13.234375 47.609375 19.390625 \r\nQ 47.609375 24.75 44.3125 27.78125 \r\nQ 41.015625 30.8125 33.5 32.328125 \r\nL 27.484375 33.5 \r\nQ 16.453125 35.6875 11.515625 40.375 \r\nQ 6.59375 45.0625 6.59375 53.421875 \r\nQ 6.59375 63.09375 13.40625 68.65625 \r\nQ 20.21875 74.21875 32.171875 74.21875 \r\nQ 37.3125 74.21875 42.625 73.28125 \r\nQ 47.953125 72.359375 53.515625 70.515625 \r\nz\r\n\" id=\"DejaVuSans-83\"/>\r\n     </defs>\r\n     <use xlink:href=\"#DejaVuSans-66\"/>\r\n     <use x=\"66.853516\" xlink:href=\"#DejaVuSans-79\"/>\r\n     <use x=\"145.564453\" xlink:href=\"#DejaVuSans-76\"/>\r\n     <use x=\"201.277344\" xlink:href=\"#DejaVuSans-76\"/>\r\n     <use x=\"256.990234\" xlink:href=\"#DejaVuSans-32\"/>\r\n     <use x=\"288.777344\" xlink:href=\"#DejaVuSans-76\"/>\r\n     <use x=\"344.490234\" xlink:href=\"#DejaVuSans-73\"/>\r\n     <use x=\"373.982422\" xlink:href=\"#DejaVuSans-78\"/>\r\n     <use x=\"448.787109\" xlink:href=\"#DejaVuSans-69\"/>\r\n     <use x=\"511.970703\" xlink:href=\"#DejaVuSans-83\"/>\r\n    </g>\r\n   </g>\r\n  </g>\r\n </g>\r\n <defs>\r\n  <clipPath id=\"pf9773a3fc8\">\r\n   <rect height=\"217.44\" width=\"334.8\" x=\"28.942188\" y=\"22.318125\"/>\r\n  </clipPath>\r\n </defs>\r\n</svg>\r\n", "image/png": "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****************************+7jxS+BDOopAnhz6Lh5cLbzmZiAzw47LNX0KH8dCsvA97/dHTxGeaeebKdqTnFZJvsZOaW0CQZ+2qLWWZLWTnWwn3c3DRNfMUbJoOPW6DVtWX78vfqwUnuXTsUCv7FAqFopiLaiYf6OHM37oB/CmGElic8dHPg+9cbwfPEPiv4sCeOdFxeLsaGN4huCSHTOkaqzWluF5rRTlqyiElrH5Xez/4aYf6N+/di7FZM/SenrU1UaFQKICLTOR1OkGYrythvq7odJr/PdzXjWWHcznS8lYtaiV2S5lrMk2F/LM3iQndQ3Ex6Ev86GcX1q4JxQ+Iat01dhssfxF2/AJ9HnAofXD20qXk/rsCl441y4ipUCgUFXFRiTzA6E4hXNnpTCreIW21pF7PnuihzeZ/mQjHVpec334qg0KrveSa4pl8XA0XX0tT/ICo0l1TkAOzboINn0Kve2DYq9X2a83IIP5/jwPg1rvy8EqFQqFwlItO5J8c2ZanRrbDlptL0ltv0fvNR7irkw8HMwXctRy8msIv18DOWcCZWXfzQC0bo5tBTxt3E3FpObW2ITbdhKezE96ulSyg5p2G70fB4WUw+gMY84FDyceyFy4EIOLHH/GZNKnW9ikUCkUxF9XCazE5q1aR9NLLWFNTAeizcRHfeQ4ky7kJ3nf9A7Nvhj/uB88Q4jICcXbSEehugJMbYdGjLLMdJGufN0S/Cl1vAEPNFmDjMsyE+blVHCOfn6U9ZNKOwE1zHFpoBW0nbebv83Hp0gX3PuUyNSsUCkWtuOhm8vb8fOL/9zh6b28iZ8/C+5oJhK34k6uPrtXcKC7ecP1vWjz6nFsIPfEHH7p+j5jaHn4YBfmZzA+4nxOEwuLH4P1WsGBKOV9+VcRmmCos8UfaUfhxDCTvhUkzHBZ4gPw9eyg4dKgkRYFCoVDUBxfdTD5v40ak2UzQ//0frl264Ny2LZkHjzL+6Dpi0810bOoNzh5w01z4cQy3p76LWbhC65HQ9kpoM4rDq5N4Zu0A9t/uin7ffNi/SEv72/s+GPlmlXHsUkpi080MaFWqwIclHw4s1lIW2K3aQ6bNiBqNK/P33xEuLniNGV3bH41CoVCU46Kbyef+twqdm1vJwqTO2RmvywcTYkojKS75TEPfZvBQNNfzNh90WgiTfoKu14OrD+G+bhTaBEmB/WDc5/D4fi36ZctXmqvFkl/uvlkmC5mmQk6kmTBbbGcWXde8Dx+2gd/v0h4ud/5TY4G3m81kL16C18gRKmxSoVDUKxfdTN6em4PHkMHojMaSYz7du5IDmHbvgVFnsjpmWfVsym/G0ED/Mn0UC3RsuolQH1cwusOotyG4I/z5IPzzHIx+H4oKhUxfdZR3/z5Qpo9m/m5wcgOsfANaDYd+D0HkIIcWWM8md9Uq7Lm5eE9QrhqFQlG/XHQiHzp1KtJuL3PMtSimXH/kYJnjlW1aKo5vj0030adFqQdA95s1f/qmLyBpl5brPbgjO2MzCfZyZsrglgC4GfUMiPSE758Er1CY9DMYa5Di4CzMO3chnJ1xi+pZ6z4UCoWiIi46kQcQZ82W9d7eZHkH4B5/oszxyjYthfi4IEQlsfIj39KqNf39DHw1CFoMwXJ6Mu1DIrmjf/Mz7f55HlL2ajnh6yDwAPn79+Pcpg3C6aL8dSgUiguYRqMq+UFN8UxLRkpZEtpY2aYlZyc9TbxciK1o16sQ0GWSFhmzbipsn8Fb+dvZ4X49HDNDYZ4229/4mbbJqYb+97ORUpK/fz9eV15Zp34UCoWiIhqNyBMaRvDJI6TlFRJQlNemqk1L4b5uxFWVv8bND0a8QU6bCSR8fy+jEr6An784c77NKBj+Wp3NtsTHY8/OLqn+pFAoFPVJoxF5l2bN8F5lIvZkEgEdtbqrsVVsWgrzc2Xj0bRq+z1paMWEwtf4cVwgQ5oUaBE0rr7gG1kvducXpRV26dSpXvpTKBSK0lx0IZSV4d1a85enHDwGwNrDqaw8kFLxpiW0xdik7HwKrFqN2F83nyIlp2zoZL7FxnMLdgPgH94Wmg/U/PX1JPAAph0xCFdXXNq1rbc+FQqFophGI/KhHbWCIid2aqGOH/yjRdoMahNYYfsOIZ5ICVuOp5OQaea5BbuZGx1Xps3m4+nsisvC6KSjZZB7g9ht3rED1y5d1KKrQqFoEBqNyHu0bE6Bixv2zRsxF9qIzTBzQ+8IbunTrML2l7cLwsfNwOytsZxK1xZgY9PLLsQWf7/6qSG4GetfhO35+eTv349rt2713rdCoVBAIxJ5ndGIuHwYfWJjmL/+EOl5hVWmAnZ20jO+WyjL9iazJz4LoFy0TWyGCaNeR3AtK0hVR8Hhw2CzqQpQCoWiwWg0Ig/Q+pbrcbFZ2DHjd6D6oh6TosIptNn5du1xoHy1qLh0M6GlCpTUNwUHNZeSS7t2DdK/QqFQ1EnkhRCvCCHihRAxRa/zml3LtXs38oJC6X1wI0BJFajK6NDUi06hXiRlawuuCZlmbPYzBb4rzTZZT+QfPIRwc8MQVn3FKIVCoagN9TGT/0hK2a3o9Vc99FdrhBD4XzuBzmnHCMk7TXglAm3LyiL/wAGk3c7kqPCS41a75GhqLtn5FrLzLcSmm6p9UNQWKSX5e/fi0rp1uR28CoVCUV80OnUJu34idiEYHb8dP3djufPZS5dyePAQjo+fQOKLLzK2QxDOTjotURkw4qM1dHllGV1eWUaGyUKzBhL55DfexLx9O259+jRI/wqFQgH1sxnqISHErUA08ISUMqOiRkKIe4F7ASIiIurhthVjCA7GeFkfrjm8A5mfD87O5KxYgXlHDOadOzFv24Zrt264dutG+o8/YomN45fHXyI4PIT1R0+TV2At6ctJJxjfPbTebbRmZJA5Zw7e48YR+MjD9d6/QqFQFCOklFU3EOJfoEkFp54HNgGnAQm8DoRIKe+s7qZRUVEyOjq65tY6iGnrVk7ecit+t92GtFjI+PVXhMGAc7t2eF5xBf533oEwGsn6808SX3oZvZ8fTd95B/fLejeYTaVJ/3kGyW+9RfM/FqhFV4VC4TBCiG1SyqiaXFPtTF5K6VANOyHEN8Dimty8oXDr1QvfG28g/aefAPC77TaCnngcYSzrvvEeNw5jy1bEPfIwp267jeAXX8Dvppsa1DYpJZnz5uHSqZMSeIVC0eDUyV0jhAiRUiYWfTsB2FN3k+qH4OeeQ+fmhiEiAp/rrqu46Dbg2qkjLZcsIe7BB0md9jFeI0fiFBDQYHYV13Jt8srLDXYPhUKhKKauC6/vCSF2CyF2AZcD/6sHm+oF4eRE0JNP4jtpUqUCX4zO1ZXgF15AFhSQ+OJLVOfCqguZ84pruY5psHsoFApFMXUSeSnlLVLKzlLKLlLKq0vN6i86nFu0IOiJx8n97z8yZ8+utr01NZX8g4dqdA+7yUT24sV4jRyparkqFIpzgsqKVQrfW24hd/Uakl55FVP0NgIfexTsdrKXLMG0dSu2rGzNlWNwInf1GpCS8Olf4DFwoEP9565fjz0vD+8J4xt2IAqFQlGEEvlSCJ2O0E8+Ie2bb0j/6Sey//oL7HYQAue2bXEKCMCSkoI9NxevUaPI27CBhP97hpZL/0Lv7V1t/3nr16Nzc8Otp6rlqlAozg1K5M9C7+FO0P8ew/fGG8j47Tf0Xt54jRmDITioXNv8ffs4fu1EEp55ltAP3kfnXnU64rz1G3Dr0wdhKF+pSqFQKBqCRrfjtb4wBAcT9Nhj+N95R4UCD+DSoQPBzz1H7urVnLjhRsx792KJj6+wbd6WLVhiY/EYMrghzVYoFIoyKJGvI3633Ez4119jSUrixLUTOTp6DKYdO8q1S/vyK/QBAXhfffV5sFKhUFyqKJGvBzwG9Kf5vLn43z8FnasrCU89jSwsLDlfeOoUeRs24HfzTehcGiY3vUKhUFSEEvl6whgRQdCjj9L0/fewxMWROn069rw8ADLnzgMh8B4//vwaqVAoLjnUwms94z5gAJ6jRpE2/UvSpn+J3tsbW1YWniNGYGhSUQoghUKhaDiUyNczQghCP5pK7vhxFBw+jCUhAb23NwH333++TVMoFJcgSuQbACEEnkOG4DlkyPk2RaFQXOIon7xCoVA0YpTIKxQKRSNGibxCoVA0YpTIKxQKRSNGibxCoVA0YpTIKxQKRSNGibxCoVA0YpTIKxQKRSNGNGQ900pvKkQqcLKWlwcAp+vRnPNFYxkHqLFcyDSW8TSWcUDdxtJMShlYkwvOi8jXBSFEtJQy6nzbUVcayzhAjeVCprGMp7GMA879WJS7RqFQKBoxSuQVCoWiEXMxivzX59uAeqKxjAPUWC5kGst4Gss44ByP5aLzySsUCoXCcS7GmbxCoVAoHESJvEKhUDRmpJQN+gLCgf+A/cBe4NGi437AcuBw0VffouPDgW3A7qKvQ0v11bPo+BHgE4rcTRXcs8J2wCBgO2AFJl7E45hSdDwGWAd0uIjHcjuQWjSWGODui3gsH5UaxyEg8yIeSzNgBbALWAWEXQRjeROIBXLPOl7r//sGGEuFNtbg91LjsdSroFdibAjQo+i9Z9EffwfgPeCZouPPAO8Wve8ONC163wmIL9XXFqAvIIClwJWV3LPCdkAk0AX4uaa/7AtsHF6l2lwN/H0Rj+V24LPG8Pd1VpuHge8v1rEAc4Hbit4PBWZcBGPpU3Tfs0U+klr+3zfAWCq0sQa/lxqPpVb/WHV5AX+iPekOAiGlfogHK2grgDTAuajNgVLnbgC+quQXUmU74Mfa/LIvtHGUOr70Yh0LdRT5C2ksZ7XbAAy/WMeCNmMNK9V39oU8lrOur1BAqYf/+7qMxREbHf0bq8lYzqlPXggRifaU2wwESykTAYq+BlVwybXADillARAKxJU6F1d07GwcbVdrLoRxCCEeFEIcRZtNPHIxjwW4VgixSwgxTwgRfpGPBSFEM6A5sLJWA+GCGMvOoj4BJgCeQgj/C3gs54Q6jsVR6nXM50zkhRAewO/AY1LKbAfadwTeBe4rPlRBM1nRpQ62qxUXyjiklJ9LKVsC/we8UJ0dldh2IYxlERAppewC/Av8VJ0dldh2IYylmOuBeVJKW3V2VGLbhTCWJ4HBQogdwGAgHs0PXCPO4VganHoYi8O3quBYrcd8TkReCGFA++HMlFLOLzqcLIQIKTofAqSUah8GLABulVIeLTocB4SV6jYMSBBC6IUQMUWv1ypr14jHMQsYf7GORUqZVmqW8w3agtNFOZZSXA/8VtNxXEhjkVImSCmvkVJ2B54vOpZ1AY+lQamnsVTWd8NqWF39Uw74rwTaIsG0s46/T9lFi/eK3vtQ9FGxgr62oi1cFC9GjK7knlW2oxa+uQtpHEDrUm3GAtEX8VhCSrWZAGy6WMdSdK4tcIJKIkAulrGgZUrUFb1/E3jtQh9Lqfb16pOvz7FUZ6OjY67JWGo02Nq8gAFoHzV2cSa8bDTgjxaidbjoq19R+xeAvFJtY4CgonNRwB7gKPBZZf9IlbUDeqE9JfPQFkP2XqTj+BhtYSwGLbSr40X8O3m7aCw7i8bS7mIdS9G5V4B3GsH/ysSi+x0CvuWshcMLdCzvof1/24u+vlLX//sGGEuFNjakhqm0BgqFQtGIUTteFQqFohGjRF6hUCgaMUrkFQqFohGjRF6hUCgaMUrkFQqFohGjRF6hUCgaMUrkFQqFohHz/61SZI1W9EiJAAAAAElFTkSuQmCC\n"}, "metadata": {"needs_background": "light"}}], "source": ["r = s.rolling('30D')\n", "plt.plot(s)\n", "plt.title('BOLL LINES')\n", "plt.plot(r.mean())\n", "plt.plot(r.mean()+r.std()*2)\n", "plt.plot(r.mean()-r.std()*2)"]}, {"source": ["对于`shift`函数而言，作用在`datetime64`为索引的序列上时，可以指定`freq`单位进行滑动："], "cell_type": "markdown", "metadata": {}}, {"cell_type": "code", "execution_count": 81, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["2020-02-20   -1\n", "2020-02-21   -2\n", "2020-02-22   -1\n", "2020-02-25   -1\n", "2020-02-26   -2\n", "dtype: int32"]}, "metadata": {}, "execution_count": 81}], "source": ["s.shift(freq='50D').head()"]}, {"source": ["另外，`datetime64[ns]`的序列进行`diff`后就能够得到`timedelta64[ns]`的序列，这能够使用户方便地观察有序时间序列的间隔："], "cell_type": "markdown", "metadata": {}}, {"cell_type": "code", "execution_count": 82, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["0   2020-01-01\n", "1   2020-01-02\n", "2   2020-01-03\n", "3   2020-01-06\n", "4   2020-01-07\n", "dtype: datetime64[ns]"]}, "metadata": {}, "execution_count": 82}], "source": ["my_series = pd.Series(s.index)\n", "my_series.head()"]}, {"cell_type": "code", "execution_count": 83, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["0      NaT\n", "1   1 days\n", "2   1 days\n", "3   3 days\n", "4   1 days\n", "dtype: timedelta64[ns]"]}, "metadata": {}, "execution_count": 83}], "source": ["my_series.diff(1).head()"]}, {"source": ["### 2. 重采样\n", "\n", "重采样对象`resample`和第四章中分组对象`groupby`的用法类似，前者是针对时间序列的分组计算而设计的分组对象。\n", "\n", "例如，对上面的序列计算每10天的均值："], "cell_type": "markdown", "metadata": {}}, {"cell_type": "code", "execution_count": 84, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["2020-01-01   -2.000000\n", "2020-01-11   -3.166667\n", "2020-01-21   -3.625000\n", "2020-01-31   -4.000000\n", "2020-02-10   -0.375000\n", "Freq: 10D, dtype: float64"]}, "metadata": {}, "execution_count": 84}], "source": ["s.resample('10D').mean().head()"]}, {"source": ["同时，如果没有内置定义的处理函数，可以通过`apply`方法自定义："], "cell_type": "markdown", "metadata": {}}, {"cell_type": "code", "execution_count": 85, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["2020-01-01    3\n", "2020-01-11    4\n", "2020-01-21    4\n", "2020-01-31    2\n", "2020-02-10    4\n", "Freq: 10D, dtype: int32"]}, "metadata": {}, "execution_count": 85}], "source": ["s.resample('10D').apply(lambda x:x.max()-x.min()).head() # 极差"]}, {"source": ["在`resample`中要特别注意组边界值的处理情况，默认情况下起始值的计算方法是从最小值时间戳对应日期的午夜`00:00:00`开始增加`freq`，直到不超过该最小时间戳的最大时间戳，由此对应的时间戳为起始值，然后每次累加`freq`参数作为分割结点进行分组，区间情况为左闭右开。下面构造一个不均匀的例子："], "cell_type": "markdown", "metadata": {}}, {"cell_type": "code", "execution_count": 86, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["2020-01-01 08:26:35   -1\n", "2020-01-01 08:27:52   -1\n", "2020-01-01 08:29:09   -2\n", "2020-01-01 08:30:26   -3\n", "2020-01-01 08:31:43   -4\n", "Freq: 77S, dtype: int32"]}, "metadata": {}, "execution_count": 86}], "source": ["idx = pd.date_range('20200101 8:26:35', '20200101 9:31:58', freq='77s')\n", "data = np.random.randint(-1,2,len(idx)).cumsum()\n", "s = pd.Series(data,index=idx)\n", "s.head()"]}, {"source": ["下面对应的第一个组起始值为`08:24:00`，其是从当天0点增加72个`freq=7 min`得到的，如果再增加一个`freq`则超出了序列的最小时间戳`08:26:35`："], "cell_type": "markdown", "metadata": {}}, {"cell_type": "code", "execution_count": 87, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["2020-01-01 08:24:00   -1.750000\n", "2020-01-01 08:31:00   -2.600000\n", "2020-01-01 08:38:00   -2.166667\n", "2020-01-01 08:45:00    0.200000\n", "2020-01-01 08:52:00    2.833333\n", "Freq: 7T, dtype: float64"]}, "metadata": {}, "execution_count": 87}], "source": ["s.resample('7min').mean().head()"]}, {"source": ["有时候，用户希望从序列的最小时间戳开始依次增加`freq`进行分组，此时可以指定`origin`参数为`start`："], "cell_type": "markdown", "metadata": {}}, {"cell_type": "code", "execution_count": 88, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["2020-01-01 08:26:35   -2.333333\n", "2020-01-01 08:33:35   -2.400000\n", "2020-01-01 08:40:35   -1.333333\n", "2020-01-01 08:47:35    1.200000\n", "2020-01-01 08:54:35    3.166667\n", "Freq: 7T, dtype: float64"]}, "metadata": {}, "execution_count": 88}], "source": ["s.resample('7min', origin='start').mean().head()"]}, {"source": ["在返回值中，要注意索引一般是取组的第一个时间戳，但`M, A, Q, BM, BA, BQ, W`这七个是取对应区间的最后一个时间戳："], "cell_type": "markdown", "metadata": {}}, {"cell_type": "code", "execution_count": 89, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["2020-01-31    0.451613\n", "2020-02-29    0.448276\n", "2020-03-31    0.516129\n", "2020-04-30    0.566667\n", "2020-05-31    0.451613\n", "Freq: M, dtype: float64"]}, "metadata": {}, "execution_count": 89}], "source": ["s = pd.Series(np.random.randint(2,size=366), index=pd.date_range('2020-01-01', '2020-12-31'))\n", "s.resample('M').mean().head()"]}, {"cell_type": "code", "execution_count": 90, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["2020-01-01    0.451613\n", "2020-02-01    0.448276\n", "2020-03-01    0.516129\n", "2020-04-01    0.566667\n", "2020-05-01    0.451613\n", "Freq: MS, dtype: float64"]}, "metadata": {}, "execution_count": 90}], "source": ["s.resample('MS').mean().head() # 结果一样，但索引不同"]}, {"source": ["## 六、练习\n", "### Ex1：太阳辐射数据集\n", "\n", "现有一份关于太阳辐射的数据集："], "cell_type": "markdown", "metadata": {}}, {"cell_type": "code", "execution_count": 91, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["                    Data      Time  Radiation  Temperature\n", "0  9/29/2016 12:00:00 AM  23:55:26       1.21           48\n", "1  9/29/2016 12:00:00 AM  23:50:23       1.21           48\n", "2  9/29/2016 12:00:00 AM  23:45:26       1.23           48"], "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>Data</th>\n      <th>Time</th>\n      <th>Radiation</th>\n      <th>Temperature</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>0</th>\n      <td>9/29/2016 12:00:00 AM</td>\n      <td>23:55:26</td>\n      <td>1.21</td>\n      <td>48</td>\n    </tr>\n    <tr>\n      <th>1</th>\n      <td>9/29/2016 12:00:00 AM</td>\n      <td>23:50:23</td>\n      <td>1.21</td>\n      <td>48</td>\n    </tr>\n    <tr>\n      <th>2</th>\n      <td>9/29/2016 12:00:00 AM</td>\n      <td>23:45:26</td>\n      <td>1.23</td>\n      <td>48</td>\n    </tr>\n  </tbody>\n</table>\n</div>"}, "metadata": {}, "execution_count": 91}], "source": ["df = pd.read_csv('../data/solar.csv', usecols=['Data','Time','Radiation','Temperature'])\n", "df.head(3)"]}, {"source": ["1. 将`Datetime, Time`合并为一个时间列`Datetime`，同时把它作为索引后排序。\n", "2. 每条记录时间的间隔显然并不一致，请解决如下问题：\n", "* 找出间隔时间的前三个最大值所对应的三组时间戳。\n", "* 是否存在一个大致的范围，使得绝大多数的间隔时间都落在这个区间中？如果存在，请对此范围内的样本间隔秒数画出柱状图，设置`bins=50`。\n", "3. 求如下指标对应的`Series`：\n", "* 温度与辐射量的6小时滑动相关系数\n", "* 以三点、九点、十五点、二十一点为分割，该观测所在时间区间的温度均值序列\n", "* 每个观测6小时前的辐射量（一般而言不会恰好取到，此时取最近时间戳对应的辐射量）\n", "### Ex2：水果销量数据集\n", "\n", "现有一份2019年每日水果销量记录表："], "cell_type": "markdown", "metadata": {}}, {"cell_type": "code", "execution_count": 92, "metadata": {}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["         Date  Fruit  Sale\n", "0  2019-04-18  <PERSON><PERSON>    15\n", "1  2019-12-29  <PERSON><PERSON>    15\n", "2  2019-06-05  <PERSON><PERSON>    19"], "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>Date</th>\n      <th>Fruit</th>\n      <th>Sale</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>0</th>\n      <td>2019-04-18</td>\n      <td>Peach</td>\n      <td>15</td>\n    </tr>\n    <tr>\n      <th>1</th>\n      <td>2019-12-29</td>\n      <td>Peach</td>\n      <td>15</td>\n    </tr>\n    <tr>\n      <th>2</th>\n      <td>2019-06-05</td>\n      <td>Peach</td>\n      <td>19</td>\n    </tr>\n  </tbody>\n</table>\n</div>"}, "metadata": {}, "execution_count": 92}], "source": ["df = pd.read_csv('../data/fruit.csv')\n", "df.head(3)"]}, {"source": ["1. 统计如下指标：\n", "* 每月上半月（15号及之前）与下半月葡萄销量的比值\n", "* 每月最后一天的生梨销量总和\n", "* 每月最后一天工作日的生梨销量总和\n", "* 每月最后五天的苹果销量均值\n", "2. 按月计算周一至周日各品种水果的平均记录条数，行索引外层为水果名称，内层为月份，列索引为星期。\n", "3. 按天计算向前10个工作日窗口的苹果销量均值序列，非工作日的值用上一个工作日的结果填充。\n"], "cell_type": "markdown", "metadata": {}}]}