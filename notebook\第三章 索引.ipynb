import numpy as np
import pandas as pd

df = pd.read_csv('../data/learn_pandas.csv', usecols = ['School', 'Grade', 'Name', 'Gender', 'Weight', 'Transfer'])
df['Name'].head()

df[['Gender', 'Name']].head()

df.Name.head()

s = pd.Series([1, 2, 3, 4, 5, 6], index=['a', 'b', 'a', 'a', 'a', 'c'])
s['a']

s['b']

s[['c', 'b']]

s['c': 'b': -2]

try:
    s['a': 'b']
except Exception as e:
    Err_Msg = e
Err_Msg

s.sort_index()['a': 'b']

s = pd.Series(['a', 'b', 'c', 'd', 'e', 'f'], index=[1, 3, 1, 2, 5, 4])
s[1]

s[[2,3]]

s[1:-1:2]

df_demo = df.set_index('Name')
df_demo.head()

df_demo.loc['Qiang Sun'] # 多个人叫此名字

df_demo.loc['<PERSON>uan Zhao'] # 名字唯一

df_demo.loc['Qiang Sun', 'School'] # 返回Series

df_demo.loc['Quan <PERSON>', 'School'] # 返回单个元素

df_demo.loc[['Qiang Sun','Quan Zhao'], ['School','Gender']]

df_demo.loc['Gaojuan You':'Gaoqiang Qian', 'School':'Gender']

df_loc_slice_demo = df_demo.copy()
df_loc_slice_demo.index = range(df_demo.shape[0],0,-1)
df_loc_slice_demo.loc[5:3]

df_loc_slice_demo.loc[3:5] # 没有返回，说明不是整数位置切片

df_demo.loc[df_demo.Weight>70].head()

df_demo.loc[df_demo.Grade.isin(['Freshman', 'Senior'])].head()

condition_1_1 = df_demo.School == 'Fudan University'
condition_1_2 = df_demo.Grade == 'Senior'
condition_1_3 = df_demo.Weight > 70
condition_1 = condition_1_1 & condition_1_2 & condition_1_3
condition_2_1 = df_demo.School == 'Peking University'
condition_2_2 = df_demo.Grade == 'Senior'
condition_2_3 = df_demo.Weight > 80
condition_2 = condition_2_1 & (~condition_2_2) & condition_2_3
df_demo.loc[condition_1 | condition_2]

def condition(x):
    condition_1_1 = x.School == 'Fudan University'
    condition_1_2 = x.Grade == 'Senior'
    condition_1_3 = x.Weight > 70
    condition_1 = condition_1_1 & condition_1_2 & condition_1_3
    condition_2_1 = x.School == 'Peking University'
    condition_2_2 = x.Grade == 'Senior'
    condition_2_3 = x.Weight > 80
    condition_2 = condition_2_1 & (~condition_2_2) & condition_2_3
    result = condition_1 | condition_2
    return result
df_demo.loc[condition]

df_demo.loc[lambda x:'Quan Zhao', lambda x:'Gender']

df_demo.loc[lambda x: slice('Gaojuan You', 'Gaoqiang Qian')]

df_chain = pd.DataFrame([[0,0],[1,0],[-1,0]], columns=list('AB'))
df_chain
import warnings
with warnings.catch_warnings():
    warnings.filterwarnings('error')
    try:
        df_chain[df_chain.A!=0].B = 1 # 使用方括号列索引后，再使用点的列索引
    except Warning as w:
        Warning_Msg = w
print(Warning_Msg)
df_chain

df_chain.loc[df_chain.A!=0,'B'] = 1
df_chain

df_demo.iloc[1, 1] # 第二行第二列

df_demo.iloc[[0, 1], [0, 1]] # 前两行前两列

df_demo.iloc[1: 4, 2:4] # 切片不包含结束端点

df_demo.iloc[lambda x: slice(1, 4)] # 传入切片为返回值的函数

df_demo.iloc[(df_demo.Weight>80).values].head()

df_demo.School.iloc[1]

df_demo.School.iloc[1:5:2]

df.query('((School == "Fudan University")&'
         ' (Grade == "Senior")&'
         ' (Weight > 70))|'
         '((School == "Peking University")&'
         ' (Grade != "Senior")&'
         ' (Weight > 80))')

df.query('Weight > Weight.mean()').head()

df.query('(Grade not in ["Freshman", "Sophomore"]) and (Gender == "Male")').head()

df.query('Grade == ["Junior", "Senior"]').head()

low, high =70, 80
df.query('(Weight >= @low) & (Weight <= @high)').head()

df_sample = pd.DataFrame({'id': list('abcde'), 'value': [1, 2, 3, 4, 90]})
df_sample

df_sample.sample(3, replace = True, weights = df_sample.value)

np.random.seed(0)
multi_index = pd.MultiIndex.from_product([list('ABCD'), df.Gender.unique()], names=('School', 'Gender'))
multi_column = pd.MultiIndex.from_product([['Height', 'Weight'], df.Grade.unique()], names=('Indicator', 'Grade'))
df_multi = pd.DataFrame(np.c_[(np.random.randn(8,4)*5 + 163).tolist(), (np.random.randn(8,4)*5 + 65).tolist()],
                        index = multi_index, columns = multi_column).round(1)
df_multi

df_multi.index.names

df_multi.columns.names

df_multi.index.values

df_multi.columns.values

df_multi.index.get_level_values(0)

df_multi = df.set_index(['School', 'Grade'])
df_multi.head()

with warnings.catch_warnings():
    warnings.filterwarnings('error')
    try:
        df_multi.loc[('Fudan University', 'Junior')].head()
    except Warning as w:
        Warning_Msg = w
Warning_Msg

df_sorted = df_multi.sort_index()
df_sorted.loc[('Fudan University', 'Junior')].head()

df_sorted.loc[[('Fudan University', 'Senior'), ('Shanghai Jiao Tong University', 'Freshman')]].head()

df_sorted.loc[df_sorted.Weight > 70].head() # 布尔列表也是可用的

df_sorted.loc[lambda x:('Fudan University','Junior')].head()

try:
    df_multi.loc[('Fudan University', 'Senior'):].head()
except Exception as e:
    Err_Msg = e
Err_Msg

df_sorted.loc[('Fudan University', 'Senior'):].head()

df_unique = df.drop_duplicates(subset=['School','Grade']).set_index(['School', 'Grade'])
df_unique.head()

try:
    df_unique.loc[('Fudan University', 'Senior'):].head()
except Exception as e:
    Err_Msg = e
Err_Msg

df_unique.sort_index().loc[('Fudan University', 'Senior'):].head()

res = df_multi.loc[(['Peking University', 'Fudan University'], ['Sophomore', 'Junior']), :]
res.head()

res.shape

res = df_multi.loc[[('Peking University', 'Junior'), ('Fudan University', 'Sophomore')]]
res.head()

res.shape

np.random.seed(0)
L1,L2 = ['A','B','C'],['a','b','c']
mul_index1 = pd.MultiIndex.from_product([L1,L2],names=('Upper', 'Lower'))
L3,L4 = ['D','E','F'],['d','e','f']
mul_index2 = pd.MultiIndex.from_product([L3,L4],names=('Big', 'Small'))
df_ex = pd.DataFrame(np.random.randint(-9,10,(9,9)), index=mul_index1, columns=mul_index2)
df_ex

idx = pd.IndexSlice

df_ex.loc[idx['C':, ('D', 'f'):]]

df_ex.loc[idx[:'A', lambda x:x.sum()>0]] # 列和大于0

df_ex.loc[idx[:'A', 'b':], idx['E':, 'e':]]

try:
    df_ex.loc[idx[:'A', lambda x: 'b'], idx['E':, 'e':]]
except Exception as e:
    Err_Msg = e
Err_Msg

my_tuple = [('a','cat'),('a','dog'),('b','cat'),('b','dog')]
pd.MultiIndex.from_tuples(my_tuple, names=['First','Second'])

my_array = [list('aabb'), ['cat', 'dog']*2]
pd.MultiIndex.from_arrays(my_array, names=['First','Second'])

my_list1 = ['a','b']
my_list2 = ['cat','dog']
pd.MultiIndex.from_product([my_list1, my_list2], names=['First','Second'])

np.random.seed(0)
L1,L2,L3 = ['A','B'],['a','b'],['alpha','beta']
mul_index1 = pd.MultiIndex.from_product([L1,L2,L3], names=('Upper', 'Lower','Extra'))
L4,L5,L6 = ['C','D'],['c','d'],['cat','dog']
mul_index2 = pd.MultiIndex.from_product([L4,L5,L6], names=('Big', 'Small', 'Other'))
df_ex = pd.DataFrame(np.random.randint(-9,10,(8,8)), index=mul_index1,  columns=mul_index2)
df_ex

df_ex.swaplevel(0,2,axis=1).head() # 列索引的第一层和第三层交换

df_ex.reorder_levels([2,0,1],axis=0).head() # 列表数字指代原来索引中的层

df_ex.droplevel(1,axis=1)

df_ex.droplevel([0,1],axis=0)

df_ex.rename_axis(index={'Upper':'Changed_row'}, columns={'Other':'Changed_Col'}).head()

df_ex.rename(columns={'cat':'not_cat'}, level=2).head()

df_ex.rename(index=lambda x:str.upper(x), level=2).head()

new_values = iter(list('abcdefgh'))
df_ex.rename(index=lambda x:next(new_values), level=2)

df_temp = df_ex.copy()
new_idx = df_temp.index.map(lambda x: (x[0], x[1], str.upper(x[2])))
df_temp.index = new_idx
df_temp.head()

df_temp = df_ex.copy()
new_idx = df_temp.index.map(lambda x: (x[0]+'-'+x[1]+'-'+x[2]))
df_temp.index = new_idx
df_temp.head() # 单层索引

new_idx = df_temp.index.map(lambda x:tuple(x.split('-')))
df_temp.index = new_idx
df_temp.head() # 三层索引

df_new = pd.DataFrame({'A':list('aacd'), 'B':list('PQRT'), 'C':[1,2,3,4]})
df_new

df_new.set_index('A')

df_new.set_index('A', append=True)

df_new.set_index(['A', 'B'])

my_index = pd.Series(list('WXYZ'), name='D')
df_new = df_new.set_index(['A', my_index])
df_new

df_new.reset_index(['D'])

df_new.reset_index(['D'], drop=True)

df_new.reset_index()

df_reindex = pd.DataFrame({"Weight":[60,70,80], "Height":[176,180,179]}, index=['1001','1003','1002'])
df_reindex

df_reindex.reindex(index=['1001','1002','1003','1004'], columns=['Weight','Gender'])

df_existed = pd.DataFrame(index=['1001','1002','1003','1004'], columns=['Weight','Gender'])
df_reindex.reindex_like(df_existed)

df_set_1 = pd.DataFrame([[0,1],[1,2],[3,4]], index = pd.Index(['a','b','a'],name='id1'))
df_set_2 = pd.DataFrame([[4,5],[2,6],[7,1]], index = pd.Index(['b','b','c'],name='id2'))
id1, id2 = df_set_1.index.unique(), df_set_2.index.unique()
id1.intersection(id2)

id1.union(id2)

id1.difference(id2)

id1.symmetric_difference(id2)

df_set_in_col_1 = df_set_1.reset_index()
df_set_in_col_2 = df_set_2.reset_index()
df_set_in_col_1

df_set_in_col_2

df_set_in_col_1[df_set_in_col_1.id1.isin(df_set_in_col_2.id2)]

df = pd.read_csv('../data/company.csv')
df.head(3)

df = pd.read_csv('../data/chocolate.csv')
df.head(3)