****************
补充习题
****************

.. ipython:: python
    
    import numpy as np
    import pandas as pd
    import matplotlib.pyplot as plt

.. admonition:: 注意
   :class: caution

    在补充习题中尽可能不要使用任何for或while循环。

Ex1：NumPy的向量化运算
=================================

- 给定一个正整数列表，请找出缺失的最小正整数。

.. code-block:: python

    >>> arr = np.array([2,3,4])
    >>> get_miss(arr)
    1
    >>> arr = np.array([6,3,5,1,2])
    >>> get_miss(arr)
    4
    >>> arr = np.array([5,2,1,3,4])
    >>> get_miss(arr)
    6

- 设计一个生成二维NumPy数组的函数get_res()，其输入为正整数n，返回的数组构造方式如下：第1行填入1个1，第2行在上一行填入位置的下一列连续填入2个2，第3行在第二行最后一个填入位置的下一列连续填入3个3，...，第n行在第n-1行最后一个填入位置的下一列连续填入n个n。

.. code-block:: python

    >>> n = 4
    >>> get_res(n)

.. code-block:: python

    array([[1., 0., 0., 0., 0., 0., 0., 0., 0., 0.],
           [0., 2., 2., 0., 0., 0., 0., 0., 0., 0.],
           [0., 0., 0., 3., 3., 3., 0., 0., 0., 0.],
           [0., 0., 0., 0., 0., 0., 4., 4., 4., 4.]])

- 点 :math:`A` 初始位置在数轴原点处，现进行n步简单随机游走，即每一步以等概率向左或向右移动长度为1的距离，记该点最终的位置为 :math:`S_n` ，则可以证明

.. math::

    \lim_{n\rightarrow+\infty}\frac{\mathbb{E}|S_n|}{\sqrt{n}}=\sqrt{\frac{2}{\pi}}

现取n为5000进行1000次试验，且每次试验中用 :math:`\frac{1}{100}\sum_{k=1}^{100}|S_n|` 来代替 :math:`\mathbb{E}|S_n|` 。此时可以计算得到1000个 :math:`\lim_{n\rightarrow+\infty}\frac{\mathbb{E}|S_n|}{\sqrt{n}}-\sqrt{\frac{2}{\pi}}` 的估计值，请计算这些估计值的均值、0.05分位数和0.95分位数。

- 在二维平面上有n个点，每个点具有k维特征，点的坐标数据记录在node_xy中，点的特征数据记录在node_fea中。现要计算所有点的相关矩阵 :math:`S` ，点a和点b的相关系数定义如下

.. math::

    S_{ab} = \frac{\sigma_{ab}}{2} + \frac{\lambda_{ab}}{2}

其中，若记点a特征为 :math:`A` ，点b特征为 :math:`B` ，则有

.. math::

    \sigma_{ab} = \frac{\sum_{i=1}^kA_iB_i}{\sqrt{\sum_{i=1}^kA^2_i}\sqrt{\sum_{i=1}^kB^2_i}}

对于点a而言，将所有点到点a的二维平面距离进行排序，从而得到每个点到点a的距离排名，距离最近（排名为1）的点是点a自身，记点b的排名为 :math:`r^{(a)}_b` ，则定义

.. math::

    \lambda_{ab} = 1 - \frac{2\times (r^{(a)}_b-1)}{n-1}

请对于给定的node_xy和node_fea计算相关矩阵 :math:`S` 。（提示：使用np.argsort()）

.. code-block:: python

    >>> n, k = 1000, 10
    >>> node_xy = np.random.rand(n, 2)
    >>> node_fea = np.random.rand(n, k)
    >>> get_S(node_xy, node_fea)

Ex2：统计学生的成绩情况
==================================================

在data/supplement/ex2目录下存放了某校高三第一学期的学生成绩情况，包含16次周测成绩、期中考试成绩和期末考试成绩，科目一栏的成绩表示学生选课的成绩。所有的表中，相同的行表示的是同一位同学。请完成以下练习：

.. ipython:: python

    df = pd.read_csv('data/supplement/ex2/第1次周测成绩.csv')
    df.head()

- 该校高三年级中是否存在姓名相同的学生？
- 在第一次周测中，请求出每个班级选修物理或化学同学的语数英总分的平均值。哪个班级最高？
- 学生在该学期的总评计算方式是各次考试总分的加权平均值，其中周测成绩权重为50%（每次测验权重相等，即3.125%），期中权重为20%，期末权重为30%。请结合nlargest函数找出年级中总评前十的同学。
- 请统计1班到8班文理科（物化生为理科，政史地为文科）期末考试总分前5的学生，结果格式如下，括号内的为选科分数：

.. ipython:: python

    pd.DataFrame(
        {
            "1班（文）": ["王大锤：历史（102）"]+["..."]* 4,
            "1班（理）": ["..."]* 5,
            "2班（文）": ["..."]* 5,
            "...": ["..."]* 5,
            "8班（理）": ["..."]* 5,
        }
    ) # 王大锤：历史（102）只是举个例子，表示结果字符串需要按照这个格式来写

- 学生成绩的稳定性可以用每次考试在全年级相同选科学生中的总分排名标准差来度量，请计算每个班级的各科学生成绩稳定性的均值，结果格式如下：

.. ipython:: python

    pd.DataFrame(
        np.random.rand(11, 6),
        index=pd.Index(range(1, 12), name="班级"),
        columns=pd.Index(
            ["物理", "化学", "生物", "历史", "地理", "政治"],
            name="选科",
        )
    )

Ex3：统计商品的审核情况
==================================================

在data/supplement/ex3中存放了两个有关商品审核的信息表，“商品信息.csv”中记录了每个商品的ID号，唯一的识别码以及商品所属的类别，“申请与审核记录.csv”中记录了每个商品的审核信息。已知商品的审核流程如下：由申请人发起商品审核的申请，然后由审核人审核，审核的结果包括通过与不通过两种情况，若商品不通过审核则可以由另一位申请人再次发起申请，直到商品的审核通过。

.. ipython:: python

    df_info = pd.read_csv('data/supplement/ex3/商品信息.csv')
    df_info.head()

    df_record = pd.read_csv('data/supplement/ex3/申请与审核记录.csv')
    df_record.head()

- 有多少商品最终通过审核？
- 各类别商品的通过率分别为多少？
- 对于类别为“T1”且最终状态为通过的商品，平均审核次数为多少？
- 是否存在商品在上一次审核未完成时就提交了下一次审核申请？
- 请对所有审核通过的商品统计第一位申请人和最后一位审核人的信息，返回格式如下：

.. ipython:: python

    pd.DataFrame(
        {
            "ID号": ["ID 000001"]+["..."]*3,
            "类别":["T1"]+["..."]*3,
            "申请人":["\#+3((52\{"]+["..."]*3,
            "审核人":["3`}04}%@75"]+["..."]*3
        },
        index=[1,2,3,"..."]
    )

.. admonition:: 提示
   :class: hint

    groupby对象上也定义了head和tail方法。

Ex4：删除同样的行
==================================================

现有两张表，请在df1中剔除在df2中出现过的行。

.. ipython:: python

    df1 = pd.DataFrame({
        "A": [3,2,2,3,1,3],
        "B": [2,1,1,3,6,2],
        "C": [1,2,2,7,7,1],
        "D": [5,6,6,1,2,5],
    })
    df1
    df2 = pd.DataFrame({
        "A": [2,3,1],
        "B": [1,9,6],
        "C": [2,7,7],
        "D": [6,1,2],
    })
    df2

结果应当如下：

.. ipython:: python

    pd.DataFrame({
        "A": [3,3,3],
        "B": [2,3,2],
        "C": [1,7,1],
        "D": [5,1,5],
    })

Ex5：统计每个学区的开课数量
==================================================

某个城市共有4个学区，每个学区有若干学校，学校之间名字互不相同。每一条记录为该学校开设的课程，一个学校可能有多条记录，每一条记录内部的课程不会重复，但同一学校不同记录之间的课程可能重复。

.. ipython:: python

    df = pd.read_csv('data/supplement/ex5/school_course.csv')
    df.head()

课程的种类共有100门，编号为"school_1"到"school_100"。现要统计每个学区各项课程的开设学校数量，结果如下格式：

.. ipython:: python

    res = pd.DataFrame(
        0, index=["course_%d"%(i+1) for i in range(100)],
        columns=["area_%d"%(i+1) for i in range(4)]
    )
    res.head() # 若area_1共有20所学校开设了course_1，则第一个单元格为20

Ex6：捕获非零的行列索引
=================================================

给定如下的数据框，请返回非零行列组合构成的多级索引。

.. ipython:: python

    df = pd.DataFrame(
        [[0,5,0],[2,1,0],[0,0,6],[0,9,0]],
        index=list("ABCD"), columns=list("XYZ"))
    df
    res = pd.Index([
        ('X', 'B'),
        ('Y', 'A'),
        ('Y', 'B'),
        ('Y', 'D'),
        ('Z', 'C')])
    res

Ex7：分析集群日志
==================================================

某公司构建了一个分布式文件集群，它共有134台服务器构成，分别存放在五个机房，R0机房存有23台，R1机房存有16台，R2机房存有47台，R3机房存有30台，R4机房存有18台，每个机房的服务器编号从001开始。运维人员通过日志收集功能得到了如下所示的集群在2022年9月27日的文件历史传输记录，其每一行构成如下：方括号中显示了当前操作是否为向其他服务器发出文件的操作（PUSH）还是接收其他服务器文件的操作（SAVE）及其对应的操作时间。Cluster#R?#???表示了当前操作的机器编号，Cluster#R4#014表示R4机房的第14号机器；再后面的十位字符串代表了传输文件的唯一标识，如果某一个条记录为SAVE操作的机器接收了XXX文件，那么一定会有另一台机器PUSH这个XXX文件的记录；对于PUSH记录而言，最后的信息表示发出文件的大小，对于SAVE记录而言，最后的信息表示接收到文件的大小，若同一对PUSH记录和SAVE记录的文件大小不一致，那么表明本次文件传输最终处于未完成状态（Unfinished）。

.. ipython:: python

    with open("data/supplement/ex7/logs.txt", "r") as f:
        for i, txt in enumerate(f.readlines()):
            if i >= 5:
                break
            print(txt.strip())

- 使用高效方法提取日志中的信息，并注意脏数据的清洗（如时间格式错误和无效数字），将其存放为如下格式，其中push_time按时间顺序。file_id为文件唯一标识，file_size为文件实际大小，save_fize为文件最终被接收的大小，push_from表示PUSH该文件的服务器，push_to表示SAVE该文件的服务器。

.. ipython:: python

    pd.DataFrame(
        {
            "file_id": ["wfjqoIDhsD", "QigjDSEGje", "..."],
            "file_size": [6.35, 149.23, "..."],
            "save_size": [6.32, np.nan, "..."], # np.nan表示没收到
            "push_from": ["A3-007", "A0-017", "..."],
            "push_to": ["A2-012", np.nan, "..."], # np.nan表示没收到
            "push_time": pd.to_datetime([
                "20220927 01:03:55", "20220927 01:03:58", pd.NaT]),
            "save_time": pd.to_datetime([
                "20220927 01:03:57", pd.NaT, pd.NaT]),
        },
        index=[0, 1, "..."]
    ) # 数据仅为格式参考，不代表真实数据

.. admonition:: 提示
   :class: hint

    本质上是把两个一一对应的文件信息进行连接。

- 一般而言，文件在同一机房内的传输速度会比跨机房的传输速度快。请对于所有传输完成的文件，按照服务器的机房号来计算文件传输的平均速度（MB/s）。格式如下，第i行第j列表示从所有从机房i传到机房j传输完成文件的平均速度，矩阵的对角线值是否要高于非对角线值？

.. ipython:: python

    pd.DataFrame(
        np.random.rand(25).reshape(5, -1),
        index=["R%d"%i for i in range(5)],
        columns=["R%d"%i for i in range(5)],
    ) # 数据仅为格式参考，不代表真实数据

- 题干中提到，并非所有文件都会传输成功，文件传输成功（Finished）当且仅当文件大小等于接收大小；如果日志中出现了只有单条记录的文件，说明当前传输任务为Missed状态；如果文件大小不等于接收大小，说明当前任务为Unfinished状态。其中对于Unfinished状态，可以按照传输的比例超过90%和超过50%分为三档："Unfinished-Almost"、"Unfinished-Fair"和"Unfinished-Bad"。请计算每个机房的最终状态的比例，格式如下，结果先按照状态Status排序（"Finished">"Unfinished-Almost">"Unfinished-Fair">"Unfinished-Bad">"Missed"），再按照机房号排序。

.. ipython:: python

    pd.DataFrame(
        {
            "Status": ["Finished"]*3 + ["..."] + ["Missed"],
            "Room": ["R0", "R1", "R2", "...", "R5"],
            "Ratio": [0.1, 0.15, 0.1, "...", 0.05],
        },
        index=[0,1,2,"...",24]
    )

- 按小时计算每个机房发送的大文件数和接收的大文件数之差，其中大文件指大小超过800M的文件。结果的行索引是时间，列索引是机房。

- 按小时计算每台机器的空闲率，对于某一台机器而言，空闲时间指其既没有处于发送任务中又没有处于接收任务中的时间，每个小时的空闲率指当前小时的区间内空闲时间的占比。结果的行索引是时间，列索引是机器。

.. admonition:: 提示
   :class: hint

    本题涉及到了 `区间合并问题 <https://leetcode.cn/problems/merge-intervals/>`__ ，pandas的区间索引没有定义类似于merge intervals的函数，请阅读 `这个回答 <https://stackoverflow.com/questions/57882621/efficient-merge-overlapping-intervals-in-same-pandas-dataframe-with-start-and-fi>`__ 来思考如何实现这个功能。
