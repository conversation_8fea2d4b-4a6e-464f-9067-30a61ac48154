pairs = [(1, 5), (3, 8), (2, 2)]
# Your code here
result = list(map(lambda x: x[0] * x[1],pairs))
print(result)


keys = ['name', 'age', 'city']
values = ['Alice', 25, 'New York']
# Your code here
result_dict = {}
print(result_dict)


fruits = ['apple', 'banana', 'cherry']
# Your code here


import numpy as np

# Your code here
my_array = np.array([])
print(my_array)


import numpy as np

# Your code here
array_arange = []
array_linspace = []

print("arange() result:", array_arange)
print("linspace() result:", array_linspace)


import numpy as np

# Your code here
A = []
B = []
result = []
print(result)


import numpy as np

np.random.seed(42)
A = np.random.rand(4, 4)
print("Original Array:\n", A)

# Your code here

print("\nModified Array:\n", A)


import numpy as np

A = np.array([[1, 2, 3], [4, 5, 6], [7, 8, 9]])
print("Matrix A:\n", A)

# Your code here
col_means = []
row_sums = []

print("\nColumn Means:", col_means)
print("Row Sums:", row_sums)


import numpy as np

A = np.ones((4, 3))
b = np.array([10, 20, 30])

# Your code here
result = []

print(result)


import numpy as np

A = np.arange(6).reshape(3, 2)
B = np.arange(8).reshape(2, 4)

# Your code here
matrix_product = []

print(matrix_product)


import numpy as np

# 创建测试数据
np.random.seed(42)
scores = np.random.randint(40, 100, (5, 4))  # 5个学生，4门科目
print("原始分数矩阵:")
print(scores)

# Your code here
# 1. 计算每个学生的总分
student_totals = []

# 2. 计算每门科目的平均分
subject_averages = []

# 3. 找出总分最高的学生的索引
best_student_index = 0

# 4. 将所有低于60分的成绩替换为60分
# 在这里修改scores矩阵

print("\n学生总分:", student_totals)
print("科目平均分:", subject_averages)
print("总分最高的学生索引:", best_student_index)
print("\n调整后的分数矩阵:")
print(scores)
