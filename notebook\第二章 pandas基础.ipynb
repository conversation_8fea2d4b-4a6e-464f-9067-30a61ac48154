{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<center><h1>第二章 pandas基础</h1></center>"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import xlrd"]}, {"cell_type": "markdown", "metadata": {}, "source": ["在开始学习前，请保证`pandas`的版本号不低于如下所示的版本，否则请务必升级！请确认已经安装了`xlrd, xlwt, openpyxl`这三个包，其中`xlrd`版本不得高于`2.0.0`。"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["'1.2.0'"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.__version__\n", "xlrd.__version__"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 一、文件的读取和写入\n", "### 1. 文件读取"]}, {"cell_type": "markdown", "metadata": {}, "source": ["`pandas`可以读取的文件格式有很多，这里主要介绍读取`csv, excel, txt`文件。"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>col1</th>\n", "      <th>col2</th>\n", "      <th>col3</th>\n", "      <th>col4</th>\n", "      <th>col5</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2</td>\n", "      <td>a</td>\n", "      <td>1.4</td>\n", "      <td>apple</td>\n", "      <td>2020/1/1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3</td>\n", "      <td>b</td>\n", "      <td>3.4</td>\n", "      <td>banana</td>\n", "      <td>2020/1/2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>6</td>\n", "      <td>c</td>\n", "      <td>2.5</td>\n", "      <td>orange</td>\n", "      <td>2020/1/5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>5</td>\n", "      <td>d</td>\n", "      <td>3.2</td>\n", "      <td>lemon</td>\n", "      <td>2020/1/7</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   col1 col2  col3    col4      col5\n", "0     2    a   1.4   apple  2020/1/1\n", "1     3    b   3.4  banana  2020/1/2\n", "2     6    c   2.5  orange  2020/1/5\n", "3     5    d   3.2   lemon  2020/1/7"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["df_csv = pd.read_csv('../data/my_csv.csv')\n", "df_csv"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>col1</th>\n", "      <th>col2</th>\n", "      <th>col3</th>\n", "      <th>col4</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2</td>\n", "      <td>a</td>\n", "      <td>1.4</td>\n", "      <td>apple 2020/1/1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3</td>\n", "      <td>b</td>\n", "      <td>3.4</td>\n", "      <td>banana 2020/1/2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>6</td>\n", "      <td>c</td>\n", "      <td>2.5</td>\n", "      <td>orange 2020/1/5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>5</td>\n", "      <td>d</td>\n", "      <td>3.2</td>\n", "      <td>lemon 2020/1/7</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   col1 col2  col3             col4\n", "0     2    a   1.4   apple 2020/1/1\n", "1     3    b   3.4  banana 2020/1/2\n", "2     6    c   2.5  orange 2020/1/5\n", "3     5    d   3.2   lemon 2020/1/7"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["df_txt = pd.read_table('../data/my_table.txt')\n", "df_txt"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>probeID</th>\n", "      <th>Seq</th>\n", "      <th>chr</th>\n", "      <th>start</th>\n", "      <th>end</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>A01-001</td>\n", "      <td>CTTCTATCCATGGCCTCAGTTCCTGTTGGGGAGCCTCGGtgccaat...</td>\n", "      <td>chr1</td>\n", "      <td>404564</td>\n", "      <td>404664</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>A01-002</td>\n", "      <td>TTTCAGAGTGAGGTGGGACGTTCTAGGGCACCTGTTTTGCAGATGC...</td>\n", "      <td>chr1</td>\n", "      <td>729613</td>\n", "      <td>729713</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>A01-003</td>\n", "      <td>GCACATGGCAGGAGAGTCACCCCGGCTGAACGAGGCTCTGCATCTT...</td>\n", "      <td>chr1</td>\n", "      <td>959105</td>\n", "      <td>959205</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>A01-004</td>\n", "      <td>CAAGGGAGGGGTGGGCGGCAGTCTCCAAGGGGTCCTCAGAGAGGCT...</td>\n", "      <td>chr1</td>\n", "      <td>1002384</td>\n", "      <td>1002484</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>A01-005</td>\n", "      <td>ttcatgccaataaaggaaggccaccttaaatctttttaacaactgc...</td>\n", "      <td>chr1</td>\n", "      <td>1797868</td>\n", "      <td>1797968</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14395</th>\n", "      <td>H12-146</td>\n", "      <td>AAATCTTCAAGGTGTAGGTTTAAGGAGAGATATCAGTGTTGGTGTG...</td>\n", "      <td>chrY</td>\n", "      <td>18233732</td>\n", "      <td>18233832</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14396</th>\n", "      <td>H12-147</td>\n", "      <td>tcacaccacaacaatcaaaacacaagacatctgtgaccaaacctgt...</td>\n", "      <td>chrY</td>\n", "      <td>19205753</td>\n", "      <td>19205853</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14397</th>\n", "      <td>H12-148</td>\n", "      <td>ctgaaatggccttgcacacacccatactatcaaccttaagagacaa...</td>\n", "      <td>chrY</td>\n", "      <td>21433584</td>\n", "      <td>21433684</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14398</th>\n", "      <td>H12-149</td>\n", "      <td>gaaccactgcatgtcttagggcgcatttctcccaaatgccctcaca...</td>\n", "      <td>chrY</td>\n", "      <td>21461182</td>\n", "      <td>21461282</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14399</th>\n", "      <td>H12-150</td>\n", "      <td>tgtaatggcttcagttgggctgacctctagccagtgggtggcagtt...</td>\n", "      <td>chrY</td>\n", "      <td>21507209</td>\n", "      <td>21507309</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>14400 rows × 5 columns</p>\n", "</div>"], "text/plain": ["       probeID                                                Seq   chr  \\\n", "0      A01-001  CTTCTATCCATGGCCTCAGTTCCTGTTGGGGAGCCTCGGtgccaat...  chr1   \n", "1      A01-002  TTTCAGAGTGAGGTGGGACGTTCTAGGGCACCTGTTTTGCAGATGC...  chr1   \n", "2      A01-003  GCACATGGCAGGAGAGTCACCCCGGCTGAACGAGGCTCTGCATCTT...  chr1   \n", "3      A01-004  CAAGGGAGGGGTGGGCGGCAGTCTCCAAGGGGTCCTCAGAGAGGCT...  chr1   \n", "4      A01-005  ttcatgccaataaaggaaggccaccttaaatctttttaacaactgc...  chr1   \n", "...        ...                                                ...   ...   \n", "14395  H12-146  AAATCTTCAAGGTGTAGGTTTAAGGAGAGATATCAGTGTTGGTGTG...  chrY   \n", "14396  H12-147  tcacaccacaacaatcaaaacacaagacatctgtgaccaaacctgt...  chrY   \n", "14397  H12-148  ctgaaatggccttgcacacacccatactatcaaccttaagagacaa...  chrY   \n", "14398  H12-149  gaaccactgcatgtcttagggcgcatttctcccaaatgccctcaca...  chrY   \n", "14399  H12-150  tgtaatggcttcagttgggctgacctctagccagtgggtggcagtt...  chrY   \n", "\n", "          start       end  \n", "0        404564    404664  \n", "1        729613    729713  \n", "2        959105    959205  \n", "3       1002384   1002484  \n", "4       1797868   1797968  \n", "...         ...       ...  \n", "14395  18233732  18233832  \n", "14396  19205753  19205853  \n", "14397  21433584  21433684  \n", "14398  21461182  21461282  \n", "14399  21507209  21507309  \n", "\n", "[14400 rows x 5 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df_excel = pd.read_excel('E:\\北京简辑\\合成\\探针合成\\probe-flank-20240530-150.xlsx')\n", "df_excel"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>col1</th>\n", "      <th>col2</th>\n", "      <th>col3</th>\n", "      <th>col4</th>\n", "      <th>col5</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2</td>\n", "      <td>a</td>\n", "      <td>1.4</td>\n", "      <td>apple</td>\n", "      <td>2020/1/1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3</td>\n", "      <td>b</td>\n", "      <td>3.4</td>\n", "      <td>banana</td>\n", "      <td>2020/1/2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>6</td>\n", "      <td>c</td>\n", "      <td>2.5</td>\n", "      <td>orange</td>\n", "      <td>2020/1/5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>5</td>\n", "      <td>d</td>\n", "      <td>3.2</td>\n", "      <td>lemon</td>\n", "      <td>2020/1/7</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   col1 col2  col3    col4      col5\n", "0     2    a   1.4   apple  2020/1/1\n", "1     3    b   3.4  banana  2020/1/2\n", "2     6    c   2.5  orange  2020/1/5\n", "3     5    d   3.2   lemon  2020/1/7"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["df_excel = pd.read_excel('../data/my_excel.xlsx')\n", "df_excel"]}, {"cell_type": "markdown", "metadata": {}, "source": ["这里有一些常用的公共参数，`header=None`表示第一行不作为列名，`index_col`表示把某一列或几列作为索引，索引的内容将会在第三章进行详述，`usecols`表示读取列的集合，默认读取所有的列，`parse_dates`表示需要转化为时间的列，关于时间序列的有关内容将在第十章讲解，`nrows`表示读取的数据行数。上面这些参数在上述的三个函数里都可以使用。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0</th>\n", "      <th>1</th>\n", "      <th>2</th>\n", "      <th>3</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>col1</td>\n", "      <td>col2</td>\n", "      <td>col3</td>\n", "      <td>col4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>a</td>\n", "      <td>1.4</td>\n", "      <td>apple 2020/1/1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>b</td>\n", "      <td>3.4</td>\n", "      <td>banana 2020/1/2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>6</td>\n", "      <td>c</td>\n", "      <td>2.5</td>\n", "      <td>orange 2020/1/5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>d</td>\n", "      <td>3.2</td>\n", "      <td>lemon 2020/1/7</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      0     1     2                3\n", "0  col1  col2  col3             col4\n", "1     2     a   1.4   apple 2020/1/1\n", "2     3     b   3.4  banana 2020/1/2\n", "3     6     c   2.5  orange 2020/1/5\n", "4     5     d   3.2   lemon 2020/1/7"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.read_table('../data/my_table.txt', header=None) "]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>col3</th>\n", "      <th>col4</th>\n", "      <th>col5</th>\n", "    </tr>\n", "    <tr>\n", "      <th>col1</th>\n", "      <th>col2</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2</th>\n", "      <th>a</th>\n", "      <td>1.4</td>\n", "      <td>apple</td>\n", "      <td>2020/1/1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <th>b</th>\n", "      <td>3.4</td>\n", "      <td>banana</td>\n", "      <td>2020/1/2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <th>c</th>\n", "      <td>2.5</td>\n", "      <td>orange</td>\n", "      <td>2020/1/5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <th>d</th>\n", "      <td>3.2</td>\n", "      <td>lemon</td>\n", "      <td>2020/1/7</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           col3    col4      col5\n", "col1 col2                        \n", "2    a      1.4   apple  2020/1/1\n", "3    b      3.4  banana  2020/1/2\n", "6    c      2.5  orange  2020/1/5\n", "5    d      3.2   lemon  2020/1/7"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.read_csv('../data/my_csv.csv', index_col=['col1', 'col2'])"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>col1</th>\n", "      <th>col2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2</td>\n", "      <td>a</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3</td>\n", "      <td>b</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>6</td>\n", "      <td>c</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>5</td>\n", "      <td>d</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   col1 col2\n", "0     2    a\n", "1     3    b\n", "2     6    c\n", "3     5    d"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.read_table('../data/my_table.txt', usecols=['col1', 'col2'])"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>col1</th>\n", "      <th>col2</th>\n", "      <th>col3</th>\n", "      <th>col4</th>\n", "      <th>col5</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2</td>\n", "      <td>a</td>\n", "      <td>1.4</td>\n", "      <td>apple</td>\n", "      <td>2020-01-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3</td>\n", "      <td>b</td>\n", "      <td>3.4</td>\n", "      <td>banana</td>\n", "      <td>2020-01-02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>6</td>\n", "      <td>c</td>\n", "      <td>2.5</td>\n", "      <td>orange</td>\n", "      <td>2020-01-05</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>5</td>\n", "      <td>d</td>\n", "      <td>3.2</td>\n", "      <td>lemon</td>\n", "      <td>2020-01-07</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   col1 col2  col3    col4       col5\n", "0     2    a   1.4   apple 2020-01-01\n", "1     3    b   3.4  banana 2020-01-02\n", "2     6    c   2.5  orange 2020-01-05\n", "3     5    d   3.2   lemon 2020-01-07"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.read_csv('../data/my_csv.csv', parse_dates=['col5'])"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>col1</th>\n", "      <th>col2</th>\n", "      <th>col3</th>\n", "      <th>col4</th>\n", "      <th>col5</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2</td>\n", "      <td>a</td>\n", "      <td>1.4</td>\n", "      <td>apple</td>\n", "      <td>2020/1/1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3</td>\n", "      <td>b</td>\n", "      <td>3.4</td>\n", "      <td>banana</td>\n", "      <td>2020/1/2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   col1 col2  col3    col4      col5\n", "0     2    a   1.4   apple  2020/1/1\n", "1     3    b   3.4  banana  2020/1/2"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.read_excel('../data/my_excel.xlsx', nrows=2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["在读取`txt`文件时，经常遇到分隔符非空格的情况，`read_table`有一个分割参数`sep`，它使得用户可以自定义分割符号，进行`txt`数据的读取。例如，下面的读取的表以`||||`为分割："]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>col1 |||| col2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>TS |||| This is an apple.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>GQ |||| My name is <PERSON>.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>WT |||| Well done!</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>PT |||| May I help you?</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["              col1 |||| col2\n", "0  TS |||| This is an apple.\n", "1    GQ |||| My name is <PERSON>.\n", "2         WT |||| Well done!\n", "3    PT |||| May I help you?"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.read_table('../data/my_table_special_sep.txt')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["上面的结果显然不是理想的，这时可以使用`sep`，同时需要指定引擎为`python`： 因为默认的 c 引擎，可能处理某些编码时候有问题。"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>col1</th>\n", "      <th>col2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>TS</td>\n", "      <td>This is an apple.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>GQ</td>\n", "      <td>My name is <PERSON>.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>WT</td>\n", "      <td>Well done!</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>PT</td>\n", "      <td>May I help you?</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  col1               col2\n", "0   TS  This is an apple.\n", "1   GQ    My name is <PERSON>.\n", "2   WT         Well done!\n", "3   PT    May I help you?"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.read_table('../data/my_table_special_sep.txt', sep=' \\|\\|\\|\\| ', engine='python')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 【WARNING】`sep`是正则参数\n", "\n", "在使用`read_table`的时候需要注意，参数`sep`中使用的是正则表达式，因此需要对`|`进行转义变成`\\|`，否则无法读取到正确的结果。有关正则表达式的基本内容可以参考第八章或者其他相关资料。\n", "\n", "#### 【END】\n", "\n", "### 2. 数据写入\n", "\n", "一般在数据写入中，最常用的操作是把`index`设置为`False`，特别当索引没有特殊意义的时候，这样的行为能把索引在保存的时候去除。"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["df_csv.to_csv('../data/my_csv_saved.csv', index=False)\n", "df_excel.to_excel('../data/my_excel_saved.xlsx', index=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["`pandas`中没有定义`to_table`函数，但是`to_csv`可以保存为`txt`文件，并且允许自定义分隔符，常用制表符`\\t`分割："]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["df_txt.to_csv('../data/my_txt_saved.txt', sep='\\t', index=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["如果想要把表格快速转换为`markdown`和`latex`语言，可以使用`to_markdown`和`to_latex`函数，此处需要安装`tabulate`包。"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["|    |   col1 | col2   |   col3 | col4   | col5     |\n", "|---:|-------:|:-------|-------:|:-------|:---------|\n", "|  0 |      2 | a      |    1.4 | apple  | 2020/1/1 |\n", "|  1 |      3 | b      |    3.4 | banana | 2020/1/2 |\n", "|  2 |      6 | c      |    2.5 | orange | 2020/1/5 |\n", "|  3 |      5 | d      |    3.2 | lemon  | 2020/1/7 |\n"]}], "source": ["print(df_csv.to_markdown())"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\\begin{tabular}{lrlrll}\n", "\\toprule\n", "{} &  col1 & col2 &  col3 &    col4 &      col5 \\\\\n", "\\midrule\n", "0 &     2 &    a &   1.4 &   apple &  2020/1/1 \\\\\n", "1 &     3 &    b &   3.4 &  banana &  2020/1/2 \\\\\n", "2 &     6 &    c &   2.5 &  orange &  2020/1/5 \\\\\n", "3 &     5 &    d &   3.2 &   lemon &  2020/1/7 \\\\\n", "\\bottomrule\n", "\\end{tabular}\n", "\n"]}], "source": ["print(df_csv.to_latex())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 二、基本数据结构\n", "`pandas`中具有两种基本的数据存储结构，存储一维`values`的`Series`和存储二维`values`的`DataFrame`，在这两种结构上定义了很多的属性和方法。\n", "\n", "### 1. Series\n", "`Series`一般由四个部分组成，分别是序列的值`data`、索引`index`、存储类型`dtype`、序列的名字`name`。其中，索引也可以指定它的名字，默认为空。"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["my_idx\n", "id1              100\n", "20                 a\n", "third    {'dic1': 5}\n", "Name: my_name, dtype: object"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["s = pd.Series(data = [100, 'a', {'dic1':5}],\n", "              index = pd.Index(['id1', 20, 'third'], name='my_idx'),\n", "              dtype = 'object',\n", "              name = 'my_name')\n", "s"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["id1              100\n", "20                 a\n", "third    {'dic1': 5}\n", "Name: my_name, dtype: object"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["s = pd.Series(data = [100, 'a', {'dic1':5}],\n", "              index = ['id1', 20, 'third'],\n", "              dtype = 'object',\n", "              name = 'my_name')\n", "s"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 【NOTE】`object`类型\n", "\n", "`object`代表了一种混合类型，正如上面的例子中存储了整数、字符串以及`Python`的字典数据结构。此外，目前`pandas`把纯字符串序列也默认认为是一种`object`类型的序列，但它也可以用`string`类型存储，文本序列的内容会在第八章中讨论。\n", "\n", "#### 【END】\n", "\n", "对于这些属性，可以通过 . 的方式来获取："]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([100, 'a', {'dic1': 5}], dtype=object)"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["s.values"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['id1', 20, 'third'], dtype='object', name='my_idx')"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["s.index"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["dtype('O')"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["s.dtype"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/plain": ["'my_name'"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["s.name"]}, {"cell_type": "markdown", "metadata": {}, "source": ["利用`.shape`可以获取序列的长度："]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/plain": ["(3,)"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["s.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["索引是`pandas`中最重要的概念之一，它将在第三章中被详细地讨论。如果想要取出单个索引对应的值，可以通过`[index_item]`可以取出。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2. DataFrame\n", "`DataFrame`在`Series`的基础上增加了列索引，一个数据框可以由二维的`data`与行列索引来构造："]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>col_0</th>\n", "      <th>col_1</th>\n", "      <th>col_2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>row_0</th>\n", "      <td>1</td>\n", "      <td>a</td>\n", "      <td>1.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>row_1</th>\n", "      <td>2</td>\n", "      <td>b</td>\n", "      <td>2.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>row_2</th>\n", "      <td>3</td>\n", "      <td>c</td>\n", "      <td>3.2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       col_0 col_1  col_2\n", "row_0      1     a    1.2\n", "row_1      2     b    2.2\n", "row_2      3     c    3.2"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["data = [[1, 'a', 1.2], [2, 'b', 2.2], [3, 'c', 3.2]]\n", "df = pd.DataFrame(data = data,\n", "                  index = ['row_%d'%i for i in range(3)],\n", "                  columns=['col_0', 'col_1', 'col_2'])\n", "df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["但一般而言，更多的时候会采用从列索引名到数据的映射来构造数据框，同时再加上行索引："]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>col_0</th>\n", "      <th>col_1</th>\n", "      <th>col_2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>row_0</th>\n", "      <td>1</td>\n", "      <td>a</td>\n", "      <td>1.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>row_1</th>\n", "      <td>2</td>\n", "      <td>b</td>\n", "      <td>2.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>row_2</th>\n", "      <td>3</td>\n", "      <td>c</td>\n", "      <td>3.2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       col_0 col_1  col_2\n", "row_0      1     a    1.2\n", "row_1      2     b    2.2\n", "row_2      3     c    3.2"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.DataFrame(data = {'col_0': [1,2,3],\n", "                          'col_1':list('abc'),\n", "                          'col_2': [1.2, 2.2, 3.2]},\n", "                  index = ['row_%d'%i for i in range(3)])\n", "df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["由于这种映射关系，在`DataFrame`中可以用`[col_name]`与`[col_list]`来取出相应的列与由多个列组成的表，结果分别为`Series`和`DataFrame`："]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["pandas.core.series.Series"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["type(df['col_0'])"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>col_0</th>\n", "      <th>col_1</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>row_0</th>\n", "      <td>1</td>\n", "      <td>a</td>\n", "    </tr>\n", "    <tr>\n", "      <th>row_1</th>\n", "      <td>2</td>\n", "      <td>b</td>\n", "    </tr>\n", "    <tr>\n", "      <th>row_2</th>\n", "      <td>3</td>\n", "      <td>c</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       col_0 col_1\n", "row_0      1     a\n", "row_1      2     b\n", "row_2      3     c"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["df[['col_0', 'col_1']]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["与`Series`类似，在数据框中同样可以取出相应的属性："]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[1, 'a', 1.2],\n", "       [2, 'b', 2.2],\n", "       [3, 'c', 3.2]], dtype=object)"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["df.values"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['row_0', 'row_1', 'row_2'], dtype='object')"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["df.index"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['col_0', 'col_1', 'col_2'], dtype='object')"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["df.columns"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"data": {"text/plain": ["col_0      int64\n", "col_1     object\n", "col_2    float64\n", "dtype: object"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["df.dtypes # 返回的是值为相应列数据类型的Series"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"text/plain": ["(3, 3)"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["df.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["通过`.T`可以把`DataFrame`进行转置："]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>row_0</th>\n", "      <th>row_1</th>\n", "      <th>row_2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>col_0</th>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>col_1</th>\n", "      <td>a</td>\n", "      <td>b</td>\n", "      <td>c</td>\n", "    </tr>\n", "    <tr>\n", "      <th>col_2</th>\n", "      <td>1.2</td>\n", "      <td>2.2</td>\n", "      <td>3.2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      row_0 row_1 row_2\n", "col_0     1     2     3\n", "col_1     a     b     c\n", "col_2   1.2   2.2   3.2"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["df.T"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 三、常用基本函数\n", "为了进行举例说明，在接下来的部分和其余章节都将会使用一份`learn_pandas.csv`的虚拟数据集，它记录了四所学校学生的体测个人信息。"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['School', 'Grade', 'Name', 'Gender', 'Height', 'Weight', 'Transfer',\n", "       'Test_Number', 'Test_Date', 'Time_Record'],\n", "      dtype='object')"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.read_csv('../data/learn_pandas.csv')\n", "df.columns"]}, {"cell_type": "markdown", "metadata": {}, "source": ["上述列名依次代表学校、年级、姓名、性别、身高、体重、是否为转系生、体测场次、测试时间、1000米成绩，本章只需使用其中的前七列。"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [], "source": ["df = df[df.columns[:7]]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1. 汇总函数\n", "`head, tail`函数分别表示返回表或者序列的前`n`行和后`n`行，其中`n`默认为5："]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>School</th>\n", "      <th>Grade</th>\n", "      <th>Name</th>\n", "      <th>Gender</th>\n", "      <th>Height</th>\n", "      <th>Weight</th>\n", "      <th>Transfer</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Shanghai Jiao Tong University</td>\n", "      <td>Freshman</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Female</td>\n", "      <td>158.9</td>\n", "      <td>46.0</td>\n", "      <td>N</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Peking University</td>\n", "      <td>Freshman</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Male</td>\n", "      <td>166.5</td>\n", "      <td>70.0</td>\n", "      <td>N</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                          School     Grade            Name  Gender  Height  \\\n", "0  Shanghai Jiao Tong University  Freshman    <PERSON><PERSON><PERSON>  Female   158.9   \n", "1              Peking University  Freshman  <PERSON><PERSON><PERSON>    Male   166.5   \n", "\n", "   Weight Transfer  \n", "0    46.0        N  \n", "1    70.0        N  "]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head(2)"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>School</th>\n", "      <th>Grade</th>\n", "      <th>Name</th>\n", "      <th>Gender</th>\n", "      <th>Height</th>\n", "      <th>Weight</th>\n", "      <th>Transfer</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>197</th>\n", "      <td>Shanghai Jiao Tong University</td>\n", "      <td>Senior</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Female</td>\n", "      <td>153.9</td>\n", "      <td>45.0</td>\n", "      <td>N</td>\n", "    </tr>\n", "    <tr>\n", "      <th>198</th>\n", "      <td>Shanghai Jiao Tong University</td>\n", "      <td>Senior</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Male</td>\n", "      <td>175.3</td>\n", "      <td>71.0</td>\n", "      <td>N</td>\n", "    </tr>\n", "    <tr>\n", "      <th>199</th>\n", "      <td>Tsinghua University</td>\n", "      <td>Sophomore</td>\n", "      <td>Chunpeng Lv</td>\n", "      <td>Male</td>\n", "      <td>155.7</td>\n", "      <td>51.0</td>\n", "      <td>N</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                            School      Grade            Name  Gender  Height  \\\n", "197  Shanghai Jiao Tong University     Senior  <PERSON><PERSON><PERSON>  Female   153.9   \n", "198  Shanghai Jiao Tong University     Senior   <PERSON><PERSON>   175.3   \n", "199            Tsinghua University  Sophomore     Chunpeng Lv    Male   155.7   \n", "\n", "     Weight Transfer  \n", "197    45.0        N  \n", "198    71.0        N  \n", "199    51.0        N  "]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["df.tail(3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["`info, describe`分别返回表的信息概况和表中数值列对应的主要统计量 ："]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 200 entries, 0 to 199\n", "Data columns (total 7 columns):\n", " #   Column    Non-Null Count  Dtype  \n", "---  ------    --------------  -----  \n", " 0   School    200 non-null    object \n", " 1   Grade     200 non-null    object \n", " 2   Name      200 non-null    object \n", " 3   Gender    200 non-null    object \n", " 4   Height    183 non-null    float64\n", " 5   Weight    189 non-null    float64\n", " 6   Transfer  188 non-null    object \n", "dtypes: float64(2), object(5)\n", "memory usage: 11.1+ KB\n"]}], "source": ["df.info()"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Height</th>\n", "      <th>Weight</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>183.000000</td>\n", "      <td>189.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>163.218033</td>\n", "      <td>55.015873</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>8.608879</td>\n", "      <td>12.824294</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>145.400000</td>\n", "      <td>34.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>157.150000</td>\n", "      <td>46.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>161.900000</td>\n", "      <td>51.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>167.500000</td>\n", "      <td>65.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>193.900000</td>\n", "      <td>89.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           Height      Weight\n", "count  183.000000  189.000000\n", "mean   163.218033   55.015873\n", "std      8.608879   12.824294\n", "min    145.400000   34.000000\n", "25%    157.150000   46.000000\n", "50%    161.900000   51.000000\n", "75%    167.500000   65.000000\n", "max    193.900000   89.000000"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["df.describe()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 【NOTE】更全面的数据汇总\n", "\n", "`info, describe`只能实现较少信息的展示，如果想要对一份数据集进行全面且有效的观察，特别是在列较多的情况下，推荐使用[pandas-profiling](https://pandas-profiling.github.io/pandas-profiling/docs/)包，它将在第十一章被再次提到。\n", "\n", "#### 【END】\n", "\n", "### 2. 特征统计函数\n", "在`Series`和`DataFrame`上定义了许多统计函数，最常见的是`sum, mean, median, var, std, max, min`。例如，选出身高和体重列进行演示："]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["Height    163.218033\n", "Weight     55.015873\n", "dtype: float64"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["df_demo = df[['Height', 'Weight']]\n", "df_demo.mean()"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["Height    193.9\n", "Weight     89.0\n", "dtype: float64"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["df_demo.max()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["此外，需要介绍的是`quantile, count, idxmax`这三个函数，它们分别返回的是分位数、非缺失值个数、最大值对应的索引："]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"data": {"text/plain": ["Height    167.5\n", "Weight     65.0\n", "Name: 0.75, dtype: float64"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["df_demo.quantile(0.75)"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"data": {"text/plain": ["Height    183\n", "Weight    189\n", "dtype: int64"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["df_demo.count()"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"data": {"text/plain": ["Height    193\n", "Weight      2\n", "dtype: int64"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["df_demo.idxmax() # idxmin是对应的函数"]}, {"cell_type": "markdown", "metadata": {}, "source": ["上面这些所有的函数，由于操作后返回的是标量，所以又称为聚合函数，它们有一个公共参数`axis`，默认为0代表逐列聚合，如果设置为1则表示逐行聚合："]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"data": {"text/plain": ["0    102.45\n", "1    118.25\n", "2    138.95\n", "3     41.00\n", "4    124.00\n", "dtype: float64"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["df_demo.mean(axis=1).head() # 在这个数据集上体重和身高的均值并没有意义"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3. 唯一值函数\n", "对序列使用`unique`和`nunique`可以分别得到其唯一值组成的列表和唯一值的个数："]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['Shanghai Jiao Tong University', 'Peking University',\n", "       'Fudan University', 'Tsinghua University'], dtype=object)"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["df['School'].unique()"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["School           4\n", "Grade            4\n", "Name           170\n", "Gender           2\n", "Height         141\n", "Weight          47\n", "Transfer         2\n", "Test_Number      3\n", "Test_Date      102\n", "Time_Record    107\n", "dtype: int64"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["df.nunique()"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [{"data": {"text/plain": ["4"]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["df['School'].nunique()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["`value_counts`可以得到唯一值和其对应出现的频数："]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"data": {"text/plain": ["Tsinghua University              69\n", "Shanghai Jiao Tong University    57\n", "Fudan University                 40\n", "Peking University                34\n", "Name: School, dtype: int64"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["df['School'].value_counts()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["如果想要观察多个列组合的唯一值，可以使用`drop_duplicates`。其中的关键参数是`keep`，默认值`first`表示每个组合保留第一次出现的所在行，`last`表示保留最后一次出现的所在行，`False`表示把所有重复组合所在的行剔除。"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Gender</th>\n", "      <th>Transfer</th>\n", "      <th>Name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Female</td>\n", "      <td>N</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Male</td>\n", "      <td>N</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>Female</td>\n", "      <td>NaN</td>\n", "      <td>Peng You</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>Male</td>\n", "      <td>NaN</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>Male</td>\n", "      <td>Y</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>43</th>\n", "      <td>Female</td>\n", "      <td>Y</td>\n", "      <td><PERSON><PERSON></td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    Gender Transfer            Name\n", "0   Female        <PERSON>\n", "1     Male        N  Changqiang You\n", "12  Female      <PERSON><PERSON>\n", "21    Male      <PERSON><PERSON>\n", "36    Male        Y    <PERSON>\n", "43  Female        Y      Gao<PERSON>"]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}], "source": ["df_demo = df[['Gender','Transfer','Name']]\n", "df_demo.drop_duplicates(['Gender', 'Transfer'])"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Gender</th>\n", "      <th>Transfer</th>\n", "      <th>Name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>147</th>\n", "      <td>Male</td>\n", "      <td>NaN</td>\n", "      <td><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>150</th>\n", "      <td>Male</td>\n", "      <td>Y</td>\n", "      <td><PERSON><PERSON>g You</td>\n", "    </tr>\n", "    <tr>\n", "      <th>169</th>\n", "      <td>Female</td>\n", "      <td>Y</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>194</th>\n", "      <td>Female</td>\n", "      <td>NaN</td>\n", "      <td><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>197</th>\n", "      <td>Female</td>\n", "      <td>N</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>199</th>\n", "      <td>Male</td>\n", "      <td>N</td>\n", "      <td>Chunpeng Lv</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     Gender Transfer            Name\n", "147    Male      Na<PERSON>\n", "150    Male        Y   <PERSON>g You\n", "169  Female        <PERSON>\n", "194  Female      <PERSON><PERSON>\n", "197  Female        <PERSON>\n", "199    Male        N     Chunpeng Lv"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["df_demo.drop_duplicates(['Gender', 'Transfer'], keep='last')"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Gender</th>\n", "      <th>Transfer</th>\n", "      <th>Name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Female</td>\n", "      <td>N</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Male</td>\n", "      <td>N</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Male</td>\n", "      <td>N</td>\n", "      <td><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Male</td>\n", "      <td>N</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Female</td>\n", "      <td>N</td>\n", "      <td><PERSON><PERSON></td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Gender Transfer            Name\n", "0  Female        <PERSON>\n", "1    Male        N  Changqiang You\n", "2    Male        N         Mei Sun\n", "4    <PERSON>        <PERSON>\n", "5  Female        <PERSON>"]}, "execution_count": 50, "metadata": {}, "output_type": "execute_result"}], "source": ["df_demo.drop_duplicates(['Name', 'Gender'], keep=False).head() # 保留只出现过一次的性别和姓名组合"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [{"data": {"text/plain": ["0    Shanghai Jiao Tong University\n", "1                Peking University\n", "3                 Fudan University\n", "5              Tsinghua University\n", "Name: School, dtype: object"]}, "execution_count": 51, "metadata": {}, "output_type": "execute_result"}], "source": ["df['School'].drop_duplicates() # 在Series上也可以使用"]}, {"cell_type": "markdown", "metadata": {}, "source": ["此外，`duplicated`和`drop_duplicates`的功能类似，但前者返回了是否为唯一值的布尔列表，其`keep`参数与后者一致。其返回的序列，把重复元素设为`True`，否则为`False`。 `drop_duplicates`等价于把`duplicated`为`True`的对应行剔除。"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [{"data": {"text/plain": ["0    False\n", "1    False\n", "2     True\n", "3     True\n", "4     True\n", "dtype: bool"]}, "execution_count": 52, "metadata": {}, "output_type": "execute_result"}], "source": ["df_demo.duplicated(['Gender', 'Transfer']).head()"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [{"data": {"text/plain": ["0    False\n", "1    False\n", "2     True\n", "3    False\n", "4     True\n", "Name: School, dtype: bool"]}, "execution_count": 53, "metadata": {}, "output_type": "execute_result"}], "source": ["df['School'].duplicated().head() # 在Series上也可以使用"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4. 替换函数\n", "一般而言，替换操作是针对某一个列进行的，因此下面的例子都以`Series`举例。`pandas`中的替换函数可以归纳为三类：映射替换、逻辑替换、数值替换。其中映射替换包含`replace`方法、第八章中的`str.replace`方法以及第九章中的`cat.codes`方法，此处介绍`replace`的用法。\n", "\n", "在`replace`中，可以通过字典构造，或者传入两个列表来进行替换："]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [{"data": {"text/plain": ["0    0\n", "1    1\n", "2    1\n", "3    0\n", "4    1\n", "Name: Gender, dtype: int64"]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["df['Gender'].replace({'Female':0, 'Male':1}).head()"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [{"data": {"text/plain": ["0    0\n", "1    1\n", "2    1\n", "3    0\n", "4    1\n", "Name: Gender, dtype: int64"]}, "execution_count": 55, "metadata": {}, "output_type": "execute_result"}], "source": ["df['Gender'].replace(['Female', 'Male'], [0, 1]).head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["另外，`replace`还有一种特殊的方向替换，指定`method`参数为`ffill`则为用前面一个最近的未被替换的值进行替换，`bfill`则使用后面最近的未被替换的值进行替换。从下面的例子可以看到，它们的结果是不同的："]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [{"data": {"text/plain": ["0    a\n", "1    a\n", "2    b\n", "3    b\n", "4    b\n", "5    b\n", "6    a\n", "dtype: object"]}, "execution_count": 56, "metadata": {}, "output_type": "execute_result"}], "source": ["s = pd.Series(['a', 1, 'b', 2, 1, 1, 'a'])\n", "s.replace([1, 2], method='ffill')"]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [{"data": {"text/plain": ["0    a\n", "1    b\n", "2    b\n", "3    a\n", "4    a\n", "5    a\n", "6    a\n", "dtype: object"]}, "execution_count": 57, "metadata": {}, "output_type": "execute_result"}], "source": ["s.replace([1, 2], method='bfill')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 【WARNING】正则替换请使用`str.replace`\n", "\n", "虽然对于`replace`而言可以使用正则替换，但是当前版本下对于`string`类型的正则替换还存在`bug`，因此如有此需求，请选择`str.replace`进行替换操作，具体的方式将在第八章中讲解。\n", "\n", "#### 【END】\n", "\n", "逻辑替换包括了`where`和`mask`，这两个函数是完全对称的：`where`函数在传入条件为`False`的对应行进行替换，而`mask`在传入条件为`True`的对应行进行替换，当不指定替换值时，替换为缺失值。"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [{"data": {"text/plain": ["0    -1.0\n", "1     NaN\n", "2     NaN\n", "3   -50.0\n", "dtype: float64"]}, "execution_count": 58, "metadata": {}, "output_type": "execute_result"}], "source": ["s = pd.Series([-1, 1.2345, 100, -50])\n", "s.where(s<0)"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [{"data": {"text/plain": ["0     -1.0\n", "1    100.0\n", "2    100.0\n", "3    -50.0\n", "dtype: float64"]}, "execution_count": 59, "metadata": {}, "output_type": "execute_result"}], "source": ["s.where(s<0, 100)"]}, {"cell_type": "code", "execution_count": 60, "metadata": {}, "outputs": [{"data": {"text/plain": ["0         NaN\n", "1      1.2345\n", "2    100.0000\n", "3         NaN\n", "dtype: float64"]}, "execution_count": 60, "metadata": {}, "output_type": "execute_result"}], "source": ["s.mask(s<0)"]}, {"cell_type": "code", "execution_count": 61, "metadata": {}, "outputs": [{"data": {"text/plain": ["0    -50.0000\n", "1      1.2345\n", "2    100.0000\n", "3    -50.0000\n", "dtype: float64"]}, "execution_count": 61, "metadata": {}, "output_type": "execute_result"}], "source": ["s.mask(s<0, -50)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["需要注意的是，传入的条件只需是与被调用的`Series`索引一致的布尔序列即可："]}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [{"data": {"text/plain": ["0    -50.0000\n", "1      1.2345\n", "2    100.0000\n", "3    -50.0000\n", "dtype: float64"]}, "execution_count": 62, "metadata": {}, "output_type": "execute_result"}], "source": ["s_condition= pd.Series([True,False,False,True],index=s.index)\n", "s.mask(s_condition, -50)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["数值替换包含了`round, abs, clip`方法，它们分别表示按照给定精度四舍五入、取绝对值和截断："]}, {"cell_type": "code", "execution_count": 63, "metadata": {}, "outputs": [{"data": {"text/plain": ["0     -1.00\n", "1      1.23\n", "2    100.00\n", "3    -50.00\n", "dtype: float64"]}, "execution_count": 63, "metadata": {}, "output_type": "execute_result"}], "source": ["s = pd.Series([-1, 1.2345, 100, -50])\n", "s.round(2)"]}, {"cell_type": "code", "execution_count": 64, "metadata": {}, "outputs": [{"data": {"text/plain": ["0      1.0000\n", "1      1.2345\n", "2    100.0000\n", "3     50.0000\n", "dtype: float64"]}, "execution_count": 64, "metadata": {}, "output_type": "execute_result"}], "source": ["s.abs()"]}, {"cell_type": "code", "execution_count": 65, "metadata": {}, "outputs": [{"data": {"text/plain": ["0    0.0000\n", "1    1.2345\n", "2    2.0000\n", "3    0.0000\n", "dtype: float64"]}, "execution_count": 65, "metadata": {}, "output_type": "execute_result"}], "source": ["s.clip(0, 2) # 前两个数分别表示上下截断边界"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 【练一练】\n", "\n", "在 clip 中，超过边界的只能截断为边界值，如果要把超出边界的替换为自定义的值，应当如何做？\n", "\n", "#### 【END】\n", "\n", "### 5. 排序函数\n", "排序共有两种方式，其一为值排序，其二为索引排序，对应的函数是`sort_values`和`sort_index`。\n", "\n", "为了演示排序函数，下面先利用`set_index`方法把年级和姓名两列作为索引，多级索引的内容和索引设置的方法将在第三章进行详细讲解。"]}, {"cell_type": "code", "execution_count": 66, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>Height</th>\n", "      <th>Weight</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Grade</th>\n", "      <th>Name</th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">Freshman</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <td>158.9</td>\n", "      <td>46.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <td>166.5</td>\n", "      <td>70.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Senior</th>\n", "      <th><PERSON></th>\n", "      <td>188.9</td>\n", "      <td>89.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                         Height  Weight\n", "Grade    Name                          \n", "Freshman <PERSON><PERSON><PERSON>     158.9    46.0\n", "         <PERSON><PERSON><PERSON>   166.5    70.0\n", "Senior   <PERSON>          188.9    89.0"]}, "execution_count": 66, "metadata": {}, "output_type": "execute_result"}], "source": ["df_demo = df[['Grade', 'Name', 'Height', 'Weight']].set_index(['Grade','Name'])\n", "df_demo.head(3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["对身高进行排序，默认参数`ascending=True`为升序："]}, {"cell_type": "code", "execution_count": 67, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>Height</th>\n", "      <th>Weight</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Grade</th>\n", "      <th>Name</th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Junior</th>\n", "      <th><PERSON><PERSON></th>\n", "      <td>145.4</td>\n", "      <td>34.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Senior</th>\n", "      <th>Gaomei Lv</th>\n", "      <td>147.3</td>\n", "      <td>34.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Sophomore</th>\n", "      <th><PERSON><PERSON></th>\n", "      <td>147.8</td>\n", "      <td>34.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Senior</th>\n", "      <th>Changli Lv</th>\n", "      <td>148.7</td>\n", "      <td>41.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Sophomore</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <td>150.5</td>\n", "      <td>40.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                         Height  Weight\n", "Grade     Name                         \n", "<PERSON>    <PERSON><PERSON>      145.4    34.0\n", "Senior    <PERSON><PERSON>v       147.3    34.0\n", "<PERSON><PERSON><PERSON><PERSON>        147.8    34.0\n", "Senior    <PERSON><PERSON> Lv      148.7    41.0\n", "<PERSON>ph<PERSON><PERSON>   150.5    40.0"]}, "execution_count": 67, "metadata": {}, "output_type": "execute_result"}], "source": ["df_demo.sort_values('Height').head()"]}, {"cell_type": "code", "execution_count": 68, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>Height</th>\n", "      <th>Weight</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Grade</th>\n", "      <th>Name</th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th rowspan=\"3\" valign=\"top\">Senior</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <td>193.9</td>\n", "      <td>79.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON></th>\n", "      <td>188.9</td>\n", "      <td>89.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON></th>\n", "      <td>186.5</td>\n", "      <td>83.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Freshman</th>\n", "      <th><PERSON><PERSON></th>\n", "      <td>185.3</td>\n", "      <td>87.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Senior</th>\n", "      <th><PERSON><PERSON></th>\n", "      <td>183.9</td>\n", "      <td>87.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                        Height  Weight\n", "Grade    Name                         \n", "Senior   <PERSON><PERSON><PERSON>   193.9    79.0\n", "         <PERSON>         188.9    89.0\n", "         <PERSON><PERSON>      186.5    83.0\n", "Freshman <PERSON><PERSON>       185.3    87.0\n", "Senior   <PERSON><PERSON>     183.9    87.0"]}, "execution_count": 68, "metadata": {}, "output_type": "execute_result"}], "source": ["df_demo.sort_values('Height', ascending=False).head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["在排序中，经常遇到多列排序的问题，比如在体重相同的情况下，对身高进行排序，并且保持身高降序排列，体重升序排列："]}, {"cell_type": "code", "execution_count": 69, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>Height</th>\n", "      <th>Weight</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Grade</th>\n", "      <th>Name</th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Sophomore</th>\n", "      <th><PERSON><PERSON></th>\n", "      <td>147.8</td>\n", "      <td>34.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Senior</th>\n", "      <th>Gaomei Lv</th>\n", "      <td>147.3</td>\n", "      <td>34.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Junior</th>\n", "      <th><PERSON><PERSON></th>\n", "      <td>145.4</td>\n", "      <td>34.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Sophomore</th>\n", "      <th><PERSON><PERSON></th>\n", "      <td>150.5</td>\n", "      <td>36.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Freshman</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <td>152.4</td>\n", "      <td>38.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                       Height  Weight\n", "Grade     Name                       \n", "<PERSON><PERSON><PERSON><PERSON>      147.8    34.0\n", "Senior    <PERSON><PERSON>v     147.3    34.0\n", "<PERSON>    <PERSON><PERSON>    145.4    34.0\n", "Soph<PERSON><PERSON>    150.5    36.0\n", "Freshman  <PERSON><PERSON><PERSON>   152.4    38.0"]}, "execution_count": 69, "metadata": {}, "output_type": "execute_result"}], "source": ["df_demo.sort_values(['Weight','Height'],ascending=[True,False]).head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["索引排序的用法和值排序完全一致，只不过元素的值在索引中，此时需要指定索引层的名字或者层号，用参数`level`表示。另外，需要注意的是字符串的排列顺序由字母顺序决定。"]}, {"cell_type": "code", "execution_count": 70, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>Height</th>\n", "      <th>Weight</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Grade</th>\n", "      <th>Name</th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th rowspan=\"5\" valign=\"top\">Freshman</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <td>163.5</td>\n", "      <td>55.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <td>152.4</td>\n", "      <td>38.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <td>162.3</td>\n", "      <td>51.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Yanpeng Lv</th>\n", "      <td>NaN</td>\n", "      <td>65.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON></th>\n", "      <td>165.1</td>\n", "      <td>52.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                        Height  Weight\n", "Grade    Name                         \n", "Freshman <PERSON><PERSON><PERSON>    163.5    55.0\n", "         <PERSON><PERSON><PERSON>     152.4    38.0\n", "         <PERSON><PERSON><PERSON>   162.3    51.0\n", "         Yanpeng Lv        NaN    65.0\n", "         <PERSON><PERSON>     165.1    52.0"]}, "execution_count": 70, "metadata": {}, "output_type": "execute_result"}], "source": ["df_demo.sort_index(level=['Grade','Name'],ascending=[True,False]).head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 6. apply方法\n", "`apply`方法常用于`DataFrame`的行迭代或者列迭代，它的`axis`含义与第2小节中的统计聚合函数一致，`apply`的参数往往是一个以序列为输入的函数。例如对于`.mean()`，使用`apply`可以如下地写出："]}, {"cell_type": "code", "execution_count": 71, "metadata": {}, "outputs": [{"data": {"text/plain": ["Height    163.218033\n", "Weight     55.015873\n", "dtype: float64"]}, "execution_count": 71, "metadata": {}, "output_type": "execute_result"}], "source": ["df_demo = df[['Height', 'Weight']]\n", "def my_mean(x):\n", "     res = x.mean()\n", "     return res\n", "df_demo.apply(my_mean)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["同样的，可以利用`lambda`表达式使得书写简洁，这里的`x`就指代被调用的`df_demo`表中逐个输入的序列："]}, {"cell_type": "code", "execution_count": 72, "metadata": {}, "outputs": [{"data": {"text/plain": ["Height    163.218033\n", "Weight     55.015873\n", "dtype: float64"]}, "execution_count": 72, "metadata": {}, "output_type": "execute_result"}], "source": ["df_demo.apply(lambda x:x.mean())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["若指定`axis=1`，那么每次传入函数的就是行元素组成的`Series`，其结果与之前的逐行均值结果一致。"]}, {"cell_type": "code", "execution_count": 73, "metadata": {}, "outputs": [{"data": {"text/plain": ["0    102.45\n", "1    118.25\n", "2    138.95\n", "3     41.00\n", "4    124.00\n", "dtype: float64"]}, "execution_count": 73, "metadata": {}, "output_type": "execute_result"}], "source": ["df_demo.apply(lambda x:x.mean(), axis=1).head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["这里再举一个例子：`mad`函数返回的是一个序列中偏离该序列均值的绝对值大小的均值，例如序列1,3,7,10中，均值为5.25，每一个元素偏离的绝对值为4.25,2.25,1.75,4.75，这个偏离序列的均值为3.25。现在利用`apply`计算升高和体重的`mad`指标："]}, {"cell_type": "code", "execution_count": 74, "metadata": {}, "outputs": [{"data": {"text/plain": ["Height     6.707229\n", "Weight    10.391870\n", "dtype: float64"]}, "execution_count": 74, "metadata": {}, "output_type": "execute_result"}], "source": ["df_demo.apply(lambda x:(x-x.mean()).abs().mean())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["这与使用内置的`mad`函数计算结果一致："]}, {"cell_type": "code", "execution_count": 75, "metadata": {}, "outputs": [{"data": {"text/plain": ["Height     6.707229\n", "Weight    10.391870\n", "dtype: float64"]}, "execution_count": 75, "metadata": {}, "output_type": "execute_result"}], "source": ["df_demo.mad()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 【WARNING】谨慎使用`apply`\n", "\n", "得益于传入自定义函数的处理，`apply`的自由度很高，但这是以性能为代价的。一般而言，使用`pandas`的内置函数处理和`apply`来处理同一个任务，其速度会相差较多，因此只有在确实存在自定义需求的情境下才考虑使用`apply`。\n", "\n", "#### 【END】\n", "\n", "## 四、窗口对象\n", "`pandas`中有3类窗口，分别是滑动窗口`rolling`、扩张窗口`expanding`以及指数加权窗口`ewm`。需要说明的是，以日期偏置为窗口大小的滑动窗口将在第十章讨论，指数加权窗口见本章练习。\n", "\n", "### 1. 滑窗对象\n", "要使用滑窗函数，就必须先要对一个序列使用`.rolling`得到滑窗对象，其最重要的参数为窗口大小`window`。"]}, {"cell_type": "code", "execution_count": 76, "metadata": {}, "outputs": [{"data": {"text/plain": ["Rolling [window=3,center=False,axis=0]"]}, "execution_count": 76, "metadata": {}, "output_type": "execute_result"}], "source": ["s = pd.Series([1,2,3,4,5])\n", "roller = s.rolling(window = 3)\n", "roller"]}, {"cell_type": "markdown", "metadata": {}, "source": ["在得到了滑窗对象后，能够使用相应的聚合函数进行计算，需要注意的是窗口包含当前行所在的元素，例如在第四个位置进行均值运算时，应当计算(2+3+4)/3，而不是(1+2+3)/3："]}, {"cell_type": "code", "execution_count": 77, "metadata": {}, "outputs": [{"data": {"text/plain": ["0    NaN\n", "1    NaN\n", "2    2.0\n", "3    3.0\n", "4    4.0\n", "dtype: float64"]}, "execution_count": 77, "metadata": {}, "output_type": "execute_result"}], "source": ["roller.mean()"]}, {"cell_type": "code", "execution_count": 78, "metadata": {}, "outputs": [{"data": {"text/plain": ["0     NaN\n", "1     NaN\n", "2     6.0\n", "3     9.0\n", "4    12.0\n", "dtype: float64"]}, "execution_count": 78, "metadata": {}, "output_type": "execute_result"}], "source": ["roller.sum()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["对于滑动相关系数或滑动协方差的计算，可以如下写出："]}, {"cell_type": "code", "execution_count": 79, "metadata": {}, "outputs": [{"data": {"text/plain": ["0     NaN\n", "1     NaN\n", "2     2.5\n", "3     7.0\n", "4    12.0\n", "dtype: float64"]}, "execution_count": 79, "metadata": {}, "output_type": "execute_result"}], "source": ["s2 = pd.Series([1,2,6,16,30])\n", "roller.cov(s2)"]}, {"cell_type": "code", "execution_count": 80, "metadata": {}, "outputs": [{"data": {"text/plain": ["0         NaN\n", "1         NaN\n", "2    0.944911\n", "3    0.970725\n", "4    0.995402\n", "dtype: float64"]}, "execution_count": 80, "metadata": {}, "output_type": "execute_result"}], "source": ["roller.corr(s2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["此外，还支持使用`apply`传入自定义函数，其传入值是对应窗口的`Series`，例如上述的均值函数可以等效表示："]}, {"cell_type": "code", "execution_count": 81, "metadata": {}, "outputs": [{"data": {"text/plain": ["0    NaN\n", "1    NaN\n", "2    2.0\n", "3    3.0\n", "4    4.0\n", "dtype: float64"]}, "execution_count": 81, "metadata": {}, "output_type": "execute_result"}], "source": ["roller.apply(lambda x:x.mean())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["`shift, diff, pct_change`是一组类滑窗函数，它们的公共参数为`periods=n`，默认为1，分别表示取向前第`n`个元素的值、与向前第`n`个元素做差（与`Numpy`中不同，后者表示`n`阶差分）、与向前第`n`个元素相比计算增长率。这里的`n`可以为负，表示反方向的类似操作。"]}, {"cell_type": "code", "execution_count": 82, "metadata": {}, "outputs": [{"data": {"text/plain": ["0    NaN\n", "1    NaN\n", "2    1.0\n", "3    3.0\n", "4    6.0\n", "dtype: float64"]}, "execution_count": 82, "metadata": {}, "output_type": "execute_result"}], "source": ["s = pd.Series([1,3,6,10,15])\n", "s.shift(2)"]}, {"cell_type": "code", "execution_count": 83, "metadata": {}, "outputs": [{"data": {"text/plain": ["0     NaN\n", "1     NaN\n", "2     NaN\n", "3     9.0\n", "4    12.0\n", "dtype: float64"]}, "execution_count": 83, "metadata": {}, "output_type": "execute_result"}], "source": ["s.diff(3)"]}, {"cell_type": "code", "execution_count": 84, "metadata": {}, "outputs": [{"data": {"text/plain": ["0         NaN\n", "1    2.000000\n", "2    1.000000\n", "3    0.666667\n", "4    0.500000\n", "dtype: float64"]}, "execution_count": 84, "metadata": {}, "output_type": "execute_result"}], "source": ["s.pct_change()"]}, {"cell_type": "code", "execution_count": 85, "metadata": {}, "outputs": [{"data": {"text/plain": ["0     3.0\n", "1     6.0\n", "2    10.0\n", "3    15.0\n", "4     NaN\n", "dtype: float64"]}, "execution_count": 85, "metadata": {}, "output_type": "execute_result"}], "source": ["s.shift(-1)"]}, {"cell_type": "code", "execution_count": 86, "metadata": {}, "outputs": [{"data": {"text/plain": ["0   -5.0\n", "1   -7.0\n", "2   -9.0\n", "3    NaN\n", "4    NaN\n", "dtype: float64"]}, "execution_count": 86, "metadata": {}, "output_type": "execute_result"}], "source": ["s.diff(-2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["将其视作类滑窗函数的原因是，它们的功能可以用窗口大小为`n+1`的`rolling`方法等价代替："]}, {"cell_type": "code", "execution_count": 87, "metadata": {}, "outputs": [{"data": {"text/plain": ["0    NaN\n", "1    NaN\n", "2    1.0\n", "3    3.0\n", "4    6.0\n", "dtype: float64"]}, "execution_count": 87, "metadata": {}, "output_type": "execute_result"}], "source": ["s.rolling(3).apply(lambda x:list(x)[0]) # s.shift(2)"]}, {"cell_type": "code", "execution_count": 88, "metadata": {}, "outputs": [{"data": {"text/plain": ["0     NaN\n", "1     NaN\n", "2     NaN\n", "3     9.0\n", "4    12.0\n", "dtype: float64"]}, "execution_count": 88, "metadata": {}, "output_type": "execute_result"}], "source": ["s.rolling(4).apply(lambda x:list(x)[-1]-list(x)[0]) # s.diff(3)"]}, {"cell_type": "code", "execution_count": 89, "metadata": {}, "outputs": [{"data": {"text/plain": ["0         NaN\n", "1    2.000000\n", "2    1.000000\n", "3    0.666667\n", "4    0.500000\n", "dtype: float64"]}, "execution_count": 89, "metadata": {}, "output_type": "execute_result"}], "source": ["def my_pct(x):\n", "     L = list(x)\n", "     return L[-1]/L[0]-1\n", "s.rolling(2).apply(my_pct) # s.pct_change()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 【练一练】\n", "\n", "`rolling`对象的默认窗口方向都是向前的，某些情况下用户需要向后的窗口，例如对1,2,3设定向后窗口为2的`sum`操作，结果为3,5,NaN，此时应该如何实现向后的滑窗操作？\n", "\n", "#### 【END】\n", "\n", "### 2. 扩张窗口\n", "扩张窗口又称累计窗口，可以理解为一个动态长度的窗口，其窗口的大小就是从序列开始处到具体操作的对应位置，其使用的聚合函数会作用于这些逐步扩张的窗口上。具体地说，设序列为a1, a2, a3, a4，则其每个位置对应的窗口即\\[a1\\]、\\[a1, a2\\]、\\[a1, a2, a3\\]、\\[a1, a2, a3, a4\\]。"]}, {"cell_type": "code", "execution_count": 90, "metadata": {}, "outputs": [{"data": {"text/plain": ["0    1.000000\n", "1    2.000000\n", "2    3.333333\n", "3    5.000000\n", "dtype: float64"]}, "execution_count": 90, "metadata": {}, "output_type": "execute_result"}], "source": ["s = pd.Series([1, 3, 6, 10])\n", "s.expanding().mean()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 【练一练】\n", "\n", "`cummax, cumsum, cumprod`函数是典型的类扩张窗口函数，请使用`expanding`对象依次实现它们。\n", "\n", "#### 【END】\n", "\n", "## 五、练习\n", "### Ex1：口袋妖怪数据集\n", "现有一份口袋妖怪的数据集，下面进行一些背景说明：\n", "\n", "* `#`代表全国图鉴编号，不同行存在相同数字则表示为该妖怪的不同状态\n", "\n", "* 妖怪具有单属性和双属性两种，对于单属性的妖怪，`Type 2`为缺失值\n", "* `Total, HP, Attack, Defense, Sp. Atk, Sp. Def, Speed`分别代表种族值、体力、物攻、防御、特攻、特防、速度，其中种族值为后6项之和"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>#</th>\n", "      <th>Name</th>\n", "      <th>Type 1</th>\n", "      <th>Type 2</th>\n", "      <th>Total</th>\n", "      <th>HP</th>\n", "      <th>Attack</th>\n", "      <th>Defense</th>\n", "      <th>Sp. Atk</th>\n", "      <th>Sp. Def</th>\n", "      <th>Speed</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>Bulbasaur</td>\n", "      <td>Grass</td>\n", "      <td>Poison</td>\n", "      <td>318</td>\n", "      <td>45</td>\n", "      <td>49</td>\n", "      <td>49</td>\n", "      <td>65</td>\n", "      <td>65</td>\n", "      <td>45</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Grass</td>\n", "      <td>Poison</td>\n", "      <td>405</td>\n", "      <td>60</td>\n", "      <td>62</td>\n", "      <td>63</td>\n", "      <td>80</td>\n", "      <td>80</td>\n", "      <td>60</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>Venus<PERSON><PERSON></td>\n", "      <td>Grass</td>\n", "      <td>Poison</td>\n", "      <td>525</td>\n", "      <td>80</td>\n", "      <td>82</td>\n", "      <td>83</td>\n", "      <td>100</td>\n", "      <td>100</td>\n", "      <td>80</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   #       Name Type 1  Type 2  Total  HP  Attack  Defense  Sp. Atk  Sp. Def  \\\n", "0  1  Bulbasaur  Grass  Poison    318  45      49       49       65       65   \n", "1  2    Ivysaur  Grass  Poison    405  60      62       63       80       80   \n", "2  3   Venusaur  Grass  Poison    525  80      82       83      100      100   \n", "\n", "   Speed  \n", "0     45  \n", "1     60  \n", "2     80  "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.read_csv('../data/pokemon.csv')\n", "df.head(3)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["0      True\n", "1      True\n", "2      True\n", "3      True\n", "4      True\n", "       ... \n", "795    True\n", "796    True\n", "797    True\n", "798    True\n", "799    True\n", "Length: 800, dtype: bool"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["df[df.columns[-7:]].apply(lambda x:(2*x[0]==x.sum()), axis=1)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["Water     112\n", "Normal     98\n", "Grass      70\n", "Name: Type 1, dtype: int64"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["df['Type 1'].value_counts()[:3]"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["Type 1    Type 2  \n", "Bug       Electric    1\n", "Poison    Ground      1\n", "Normal    Water       1\n", "Poison    Bug         1\n", "          Dark        1\n", "                     ..\n", "Fighting  Psychic     1\n", "          Flying      1\n", "          Dark        1\n", "Fairy     Flying      1\n", "Water     Steel       1\n", "Length: 136, dtype: int64"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["df[['Type 1', 'Type 2']].drop_duplicates()[['Type 1', 'Type 2']].value_counts()"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['<PERSON>', '<PERSON>', '<PERSON>', 'Bug', 'Normal', 'Poison', 'Electric',\n", "       '<PERSON>', '<PERSON>', '<PERSON>', 'Psychic', 'Rock', 'Ghost', 'Ice',\n", "       '<PERSON>', '<PERSON>', '<PERSON>', 'Flying'], dtype=object)"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["df['Type 1'].unique()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["1. 对`HP, Attack, Defense, Sp<PERSON>, <PERSON>p<PERSON>, <PERSON>`进行加总，验证是否为`Total`值。\n", "\n", "2. 对于`#`重复的妖怪只保留第一条记录，解决以下问题：\n", "\n", "* 求第一属性的种类数量和前三多数量对应的种类\n", "* 求第一属性和第二属性的组合种类\n", "* 求尚未出现过的属性组合\n", "\n", "3. 按照下述要求，构造`Series`：\n", "\n", "* 取出物攻，超过120的替换为`high`，不足50的替换为`low`，否则设为`mid`\n", "* 取出第一属性，分别用`replace`和`apply`替换所有字母为大写\n", "* 求每个妖怪六项能力的离差，即所有能力中偏离中位数最大的值，添加到`df`并从大到小排序\n", "\n", "### Ex2：指数加权窗口\n", "1. 作为扩张窗口的`ewm`窗口\n", "\n", "在扩张窗口中，用户可以使用各类函数进行历史的累计指标统计，但这些内置的统计函数往往把窗口中的所有元素赋予了同样的权重。事实上，可以给出不同的权重来赋给窗口中的元素，指数加权窗口就是这样一种特殊的扩张窗口。\n", "\n", "其中，最重要的参数是`alpha`，它决定了默认情况下的窗口权重为$w_i=(1−\\alpha)^i,i\\in\\{0,1,...,t\\}$，其中$i=t$表示当前元素，$i=0$表示序列的第一个元素。\n", "\n", "从权重公式可以看出，离开当前值越远则权重越小，若记原序列为$x$，更新后的当前元素为$y_t$，此时通过加权公式归一化后可知：\n", "\n", "$$\n", "\\begin{split}y_t &=\\frac{\\sum_{i=0}^{t} w_i x_{t-i}}{\\sum_{i=0}^{t} w_i} \\\\\n", "&=\\frac{x_t + (1 - \\alpha)x_{t-1} + (1 - \\alpha)^2 x_{t-2} + ...\n", "+ (1 - \\alpha)^{t} x_{0}}{1 + (1 - \\alpha) + (1 - \\alpha)^2 + ...\n", "+ (1 - \\alpha)^{t}}\\\\\\end{split}\n", "$$\n", "\n", "对于`Series`而言，可以用`ewm`对象如下计算指数平滑后的序列："]}, {"cell_type": "code", "execution_count": 92, "metadata": {}, "outputs": [{"data": {"text/plain": ["0   -1\n", "1   -1\n", "2   -2\n", "3   -2\n", "4   -2\n", "dtype: int32"]}, "execution_count": 92, "metadata": {}, "output_type": "execute_result"}], "source": ["np.random.seed(0)\n", "s = pd.Series(np.random.randint(-1,2,30).cumsum())\n", "s.head()"]}, {"cell_type": "code", "execution_count": 93, "metadata": {}, "outputs": [{"data": {"text/plain": ["0   -1.000000\n", "1   -1.000000\n", "2   -1.409836\n", "3   -1.609756\n", "4   -1.725845\n", "dtype: float64"]}, "execution_count": 93, "metadata": {}, "output_type": "execute_result"}], "source": ["s.ewm(alpha=0.2).mean().head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["请用`expanding`窗口实现。\n", "\n", "2. 作为滑动窗口的`ewm`窗口\n", "\n", "从第1问中可以看到，`ewm`作为一种扩张窗口的特例，只能从序列的第一个元素开始加权。现在希望给定一个限制窗口`n`，只对包含自身的最近的`n`个元素作为窗口进行滑动加权平滑。请根据滑窗函数，给出新的`wi`与`yt`的更新公式，并通过`rolling`窗口实现这一功能。"]}], "metadata": {"kernelspec": {"display_name": "joyful-pandas", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}, "orig_nbformat": 2}, "nbformat": 4, "nbformat_minor": 2}