
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

    <title>Content &#8212; Joyful Pandas 1.0 documentation</title>
<script>
  document.documentElement.dataset.mode = localStorage.getItem("mode") || "";
  document.documentElement.dataset.theme = localStorage.getItem("theme") || "light"
</script>

  <!-- Loaded before other Sphinx assets -->
  <link href="../_static/styles/theme.css?digest=92025949c220c2e29695" rel="stylesheet">
<link href="../_static/styles/pydata-sphinx-theme.css?digest=92025949c220c2e29695" rel="stylesheet">


  <link rel="stylesheet"
    href="../_static/vendor/fontawesome/5.13.0/css/all.min.css">
  <link rel="preload" as="font" type="font/woff2" crossorigin
    href="../_static/vendor/fontawesome/5.13.0/webfonts/fa-solid-900.woff2">
  <link rel="preload" as="font" type="font/woff2" crossorigin
    href="../_static/vendor/fontawesome/5.13.0/webfonts/fa-brands-400.woff2">

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/plot_directive.css" />
    <link rel="stylesheet" type="text/css" href="../_static/css/s4defs-roles.css" />

  <!-- Pre-loaded scripts that we'll load fully later -->
  <link rel="preload" as="script" href="../_static/scripts/pydata-sphinx-theme.js?digest=92025949c220c2e29695">

    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/jquery.js"></script>
    <script src="../_static/underscore.js"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="../_static/doctools.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="第一章 预备知识" href="ch1.html" />
    <link rel="prev" title="Home" href="../Home.html" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<meta name="docsearch:language" content="en">
  </head>
  
  
  <body data-spy="scroll" data-target="#bd-toc-nav" data-offset="180" data-default-mode="">
    <div class="bd-header-announcement container-fluid" id="banner">
      

    </div>

    
    <nav class="bd-header navbar navbar-light navbar-expand-lg bg-light fixed-top bd-navbar" id="navbar-main"><div class="bd-header__inner container-xl">

  <div id="navbar-start">
    
    
  


<a class="navbar-brand logo" href="../index.html">
  
  
  
  
    <img src="../_static/finallogo1.svg" class="logo__image only-light" alt="Logo image">
    <img src="../_static/finallogo1.svg" class="logo__image only-dark" alt="Logo image">
  
  
</a>
    
  </div>

  <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbar-collapsible" aria-controls="navbar-collapsible" aria-expanded="false" aria-label="Toggle navigation">
    <span class="fas fa-bars"></span>
  </button>

  
  <div id="navbar-collapsible" class="col-lg-9 collapse navbar-collapse">
    <div id="navbar-center" class="mr-auto">
      
      <div class="navbar-center-item">
        <ul id="navbar-main-elements" class="navbar-nav">
    <li class="toctree-l1 nav-item">
 <a class="reference internal nav-link" href="../Home.html">
  Home
 </a>
</li>

<li class="toctree-l1 current active nav-item">
 <a class="current reference internal nav-link" href="#">
  Content
 </a>
</li>

<li class="toctree-l1 nav-item">
 <a class="reference internal nav-link" href="../Author.html">
  Author
 </a>
</li>

<li class="toctree-l1 nav-item">
 <a class="reference internal nav-link" href="../Datawhale.html">
  Datawhale
 </a>
</li>

<li class="toctree-l1 nav-item">
 <a class="reference internal nav-link" href="../pandas%E6%95%B0%E6%8D%AE%E5%A4%84%E7%90%86%E4%B8%8E%E5%88%86%E6%9E%90.html">
  pandas数据处理与分析
 </a>
</li>

<li class="toctree-l1 nav-item">
 <a class="reference internal nav-link" href="../%E8%A1%A5%E5%85%85%E4%B9%A0%E9%A2%98.html">
  补充习题
 </a>
</li>

    
    <li class="nav-item">
        <a class="nav-link nav-external" href="https://pandas.pydata.org/docs/index.html">Doc<i class="fas fa-external-link-alt"></i></a>
    </li>
    
</ul>
      </div>
      
    </div>

    <div id="navbar-end">
      
      <div class="navbar-end-item">
        <span id="theme-switch" class="btn btn-sm btn-outline-primary navbar-btn rounded-circle">
    <a class="theme-switch" data-mode="light"><i class="fas fa-sun"></i></a>
    <a class="theme-switch" data-mode="dark"><i class="far fa-moon"></i></a>
    <a class="theme-switch" data-mode="auto"><i class="fas fa-adjust"></i></a>
</span>
      </div>
      
      <div class="navbar-end-item">
        <ul id="navbar-icon-links" class="navbar-nav" aria-label="Icon Links">
        <li class="nav-item">
          <a class="nav-link" href="https://github.com/datawhalechina/joyful-pandas" rel="noopener" target="_blank" title="GitHub"><span><i class="fab fa-github-square"></i></span>
            <label class="sr-only">GitHub</label></a>
        </li>
      </ul>
      </div>
      
    </div>
  </div>
</div>
    </nav>
    

    <div class="bd-container container-xl">
      <div class="bd-container__inner row">
          

<!-- Only show if we have sidebars configured, else just a small margin  -->
<div class="bd-sidebar-primary col-12 col-md-3 bd-sidebar">
  <div class="sidebar-start-items"><form class="bd-search d-flex align-items-center" action="../search.html" method="get">
  <i class="icon fas fa-search"></i>
  <input type="search" class="form-control" name="q" id="search-input" placeholder="Search the docs ..." aria-label="Search the docs ..." autocomplete="off" >
</form><nav class="bd-links" id="bd-docs-nav" aria-label="Main navigation">
  <div class="bd-toc-item active">
    <ul class="nav bd-sidenav">
 <li class="toctree-l1">
  <a class="reference internal" href="ch1.html">
   第一章 预备知识
  </a>
 </li>
 <li class="toctree-l1">
  <a class="reference internal" href="ch2.html">
   第二章 pandas基础
  </a>
 </li>
 <li class="toctree-l1">
  <a class="reference internal" href="ch3.html">
   第三章 索引
  </a>
 </li>
 <li class="toctree-l1">
  <a class="reference internal" href="ch4.html">
   第四章 分组
  </a>
 </li>
 <li class="toctree-l1">
  <a class="reference internal" href="ch5.html">
   第五章 变形
  </a>
 </li>
 <li class="toctree-l1">
  <a class="reference internal" href="ch6.html">
   第六章 连接
  </a>
 </li>
 <li class="toctree-l1">
  <a class="reference internal" href="ch7.html">
   第七章 缺失数据
  </a>
 </li>
 <li class="toctree-l1">
  <a class="reference internal" href="ch8.html">
   第八章 文本数据
  </a>
 </li>
 <li class="toctree-l1">
  <a class="reference internal" href="ch9.html">
   第九章 分类数据
  </a>
 </li>
 <li class="toctree-l1">
  <a class="reference internal" href="ch10.html">
   第十章 时序数据
  </a>
 </li>
 <li class="toctree-l1">
  <a class="reference internal" href="%E5%8F%82%E8%80%83%E7%AD%94%E6%A1%88.html">
   参考答案
  </a>
 </li>
</ul>

  </div>
</nav>
  </div>
  <div class="sidebar-end-items">
  </div>
</div>


          


<div class="bd-sidebar-secondary d-none d-xl-block col-xl-2 bd-toc">
  
    
    <div class="toc-item">
      

<nav id="bd-toc-nav">
    
</nav>
    </div>
    
    <div class="toc-item">
      
    </div>
    
  
</div>


          
          
          <div class="bd-content col-12 col-md-9 col-xl-7">
              
              <article class="bd-article" role="main">
                
  <section id="content">
<h1>Content<a class="headerlink" href="#content" title="Permalink to this heading">#</a></h1>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="ch1.html">第一章 预备知识</a><ul>
<li class="toctree-l2"><a class="reference internal" href="ch1.html#python">一、Python基础</a><ul>
<li class="toctree-l3"><a class="reference internal" href="ch1.html#id2">1. 列表推导式与条件赋值</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch1.html#map">2. 匿名函数与map方法</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch1.html#zipenumerate">3. zip对象与enumerate方法</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="ch1.html#numpy">二、Numpy基础</a><ul>
<li class="toctree-l3"><a class="reference internal" href="ch1.html#np">1. np数组的构造</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch1.html#id3">2. np数组的变形与合并</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch1.html#id4">3. np数组的切片与索引</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch1.html#id5">4. 常用函数</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch1.html#id6">5. 广播机制</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch1.html#id7">6. 向量与矩阵的计算</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="ch1.html#id8">三、练习</a><ul>
<li class="toctree-l3"><a class="reference internal" href="ch1.html#ex1">Ex1：利用列表推导式写矩阵乘法</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch1.html#ex2">Ex2：更新矩阵</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch1.html#ex3">Ex3：卡方统计量</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch1.html#ex4">Ex4：改进矩阵计算的性能</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch1.html#ex5">Ex5：连续整数的最大长度</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="ch2.html">第二章 pandas基础</a><ul>
<li class="toctree-l2"><a class="reference internal" href="ch2.html#id1">一、文件的读取和写入</a><ul>
<li class="toctree-l3"><a class="reference internal" href="ch2.html#id2">1. 文件读取</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch2.html#id3">2. 数据写入</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="ch2.html#id4">二、基本数据结构</a><ul>
<li class="toctree-l3"><a class="reference internal" href="ch2.html#series">1. Series</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch2.html#dataframe">2. DataFrame</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="ch2.html#id5">三、常用基本函数</a><ul>
<li class="toctree-l3"><a class="reference internal" href="ch2.html#id6">1. 汇总函数</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch2.html#id7">2. 特征统计函数</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch2.html#id8">3. 唯一值函数</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch2.html#id9">4. 替换函数</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch2.html#id10">5. 排序函数</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch2.html#apply">6. apply方法</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="ch2.html#id11">四、窗口对象</a><ul>
<li class="toctree-l3"><a class="reference internal" href="ch2.html#id12">1. 滑窗对象</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch2.html#id13">2. 扩张窗口</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="ch2.html#id14">五、练习</a><ul>
<li class="toctree-l3"><a class="reference internal" href="ch2.html#ex1">Ex1：口袋妖怪数据集</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch2.html#ex2">Ex2：指数加权窗口</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="ch3.html">第三章 索引</a><ul>
<li class="toctree-l2"><a class="reference internal" href="ch3.html#id2">一、索引器</a><ul>
<li class="toctree-l3"><a class="reference internal" href="ch3.html#id3">1. 表的列索引</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch3.html#id4">2. 序列的行索引</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch3.html#loc">3. loc索引器</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch3.html#iloc">4. iloc索引器</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch3.html#query">5. query方法</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch3.html#id5">6. 随机抽样</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="ch3.html#id6">二、多级索引</a><ul>
<li class="toctree-l3"><a class="reference internal" href="ch3.html#id7">1. 多级索引及其表的结构</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch3.html#id8">2. 多级索引中的loc索引器</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch3.html#indexslice">3. IndexSlice对象</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch3.html#id9">4. 多级索引的构造</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="ch3.html#id10">三、索引的常用方法</a><ul>
<li class="toctree-l3"><a class="reference internal" href="ch3.html#id11">1. 索引层的交换和删除</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch3.html#id12">2. 索引属性的修改</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch3.html#id13">3. 索引的设置与重置</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch3.html#id14">4. 索引的变形</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="ch3.html#id15">四、索引运算</a><ul>
<li class="toctree-l3"><a class="reference internal" href="ch3.html#id16">1. 集合的运算法则</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch3.html#id17">2. 一般的索引运算</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="ch3.html#id18">五、练习</a><ul>
<li class="toctree-l3"><a class="reference internal" href="ch3.html#ex1">Ex1：公司员工数据集</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch3.html#ex2">Ex2：巧克力数据集</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="ch4.html">第四章 分组</a><ul>
<li class="toctree-l2"><a class="reference internal" href="ch4.html#id2">一、分组模式及其对象</a><ul>
<li class="toctree-l3"><a class="reference internal" href="ch4.html#id3">1. 分组的一般模式</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch4.html#id4">2. 分组依据的本质</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch4.html#groupby">3. Groupby对象</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch4.html#id5">4. 分组的三大操作</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="ch4.html#id6">二、聚合函数</a><ul>
<li class="toctree-l3"><a class="reference internal" href="ch4.html#id7">1. 内置聚合函数</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch4.html#agg">2. agg方法</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="ch4.html#id8">三、变换和过滤</a><ul>
<li class="toctree-l3"><a class="reference internal" href="ch4.html#transform">1. 变换函数与transform方法</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch4.html#id9">2. 组索引与过滤</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="ch4.html#id10">四、跨列分组</a><ul>
<li class="toctree-l3"><a class="reference internal" href="ch4.html#apply">1. apply的引入</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch4.html#id11">2. apply的使用</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="ch4.html#id12">五、练习</a><ul>
<li class="toctree-l3"><a class="reference internal" href="ch4.html#ex1">Ex1：汽车数据集</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch4.html#ex2-transform">Ex2：实现transform函数</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="ch5.html">第五章 变形</a><ul>
<li class="toctree-l2"><a class="reference internal" href="ch5.html#id2">一、长宽表的变形</a><ul>
<li class="toctree-l3"><a class="reference internal" href="ch5.html#pivot">1. pivot</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch5.html#pivot-table">2. pivot_table</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch5.html#melt">3. melt</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch5.html#wide-to-long">4. wide_to_long</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="ch5.html#id3">二、索引的变形</a><ul>
<li class="toctree-l3"><a class="reference internal" href="ch5.html#stackunstack">1. stack与unstack</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch5.html#id4">2. 聚合与变形的关系</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="ch5.html#id5">三、其他变形函数</a><ul>
<li class="toctree-l3"><a class="reference internal" href="ch5.html#crosstab">1. crosstab</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch5.html#explode">2. explode</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch5.html#get-dummies">3. get_dummies</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="ch5.html#id6">四、练习</a><ul>
<li class="toctree-l3"><a class="reference internal" href="ch5.html#ex1">Ex1：美国非法药物数据集</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch5.html#ex2-wide-to-long">Ex2：特殊的wide_to_long方法</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="ch6.html">第六章 连接</a><ul>
<li class="toctree-l2"><a class="reference internal" href="ch6.html#id2">一、关系型连接</a><ul>
<li class="toctree-l3"><a class="reference internal" href="ch6.html#id3">1. 连接的基本概念</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch6.html#id4">2. 值连接</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch6.html#id5">3. 索引连接</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="ch6.html#id6">二、方向连接</a><ul>
<li class="toctree-l3"><a class="reference internal" href="ch6.html#concat">1. concat</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch6.html#id7">2. 序列与表的合并</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="ch6.html#id8">三、类连接操作</a><ul>
<li class="toctree-l3"><a class="reference internal" href="ch6.html#id9">1. 比较</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch6.html#id10">2. 组合</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="ch6.html#id11">四、练习</a><ul>
<li class="toctree-l3"><a class="reference internal" href="ch6.html#ex1">Ex1：美国疫情数据集</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch6.html#ex2-join">Ex2：实现join函数</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="ch7.html">第七章 缺失数据</a><ul>
<li class="toctree-l2"><a class="reference internal" href="ch7.html#id2">一、缺失值的统计和删除</a><ul>
<li class="toctree-l3"><a class="reference internal" href="ch7.html#id3">1. 缺失信息的统计</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch7.html#id4">2. 缺失信息的删除</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="ch7.html#id5">二、缺失值的填充和插值</a><ul>
<li class="toctree-l3"><a class="reference internal" href="ch7.html#fillna">1. 利用fillna进行填充</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch7.html#id6">2. 插值函数</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="ch7.html#nullable">三、Nullable类型</a><ul>
<li class="toctree-l3"><a class="reference internal" href="ch7.html#id7">1. 缺失记号及其缺陷</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch7.html#id8">2. Nullable类型的性质</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch7.html#id9">3. 缺失数据的计算和分组</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="ch7.html#id10">四、练习</a><ul>
<li class="toctree-l3"><a class="reference internal" href="ch7.html#ex1">Ex1：缺失值与类别的相关性检验</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch7.html#ex2">Ex2：用回归模型解决分类问题</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="ch8.html">第八章 文本数据</a><ul>
<li class="toctree-l2"><a class="reference internal" href="ch8.html#str">一、str对象</a><ul>
<li class="toctree-l3"><a class="reference internal" href="ch8.html#id2">1. str对象的设计意图</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch8.html#id3">2. []索引器</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch8.html#string">3. string类型</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="ch8.html#id4">二、正则表达式基础</a><ul>
<li class="toctree-l3"><a class="reference internal" href="ch8.html#id5">1. 一般字符的匹配</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch8.html#id6">2. 元字符基础</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch8.html#id7">3. 简写字符集</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="ch8.html#id8">三、文本处理的五类操作</a><ul>
<li class="toctree-l3"><a class="reference internal" href="ch8.html#id9">1. 拆分</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch8.html#id10">2. 合并</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch8.html#id11">3. 匹配</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch8.html#id12">4. 替换</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch8.html#id13">5. 提取</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="ch8.html#id14">四、常用字符串函数</a><ul>
<li class="toctree-l3"><a class="reference internal" href="ch8.html#id15">1. 字母型函数</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch8.html#id16">2. 数值型函数</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch8.html#id17">3. 统计型函数</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch8.html#id18">4. 格式型函数</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="ch8.html#id19">五、练习</a><ul>
<li class="toctree-l3"><a class="reference internal" href="ch8.html#ex1">Ex1：房屋信息数据集</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch8.html#ex2">Ex2：《权力的游戏》剧本数据集</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="ch9.html">第九章 分类数据</a><ul>
<li class="toctree-l2"><a class="reference internal" href="ch9.html#cat">一、cat对象</a><ul>
<li class="toctree-l3"><a class="reference internal" href="ch9.html#id2">1. cat对象的属性</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch9.html#id3">2. 类别的增加、删除和修改</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="ch9.html#id4">二、有序分类</a><ul>
<li class="toctree-l3"><a class="reference internal" href="ch9.html#id5">1. 序的建立</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch9.html#id6">2. 排序和比较</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="ch9.html#id7">三、区间类别</a><ul>
<li class="toctree-l3"><a class="reference internal" href="ch9.html#cutqcut">1. 利用cut和qcut进行区间构造</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch9.html#id8">2. 一般区间的构造</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch9.html#id9">3. 区间的属性与方法</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="ch9.html#id10">四、练习</a><ul>
<li class="toctree-l3"><a class="reference internal" href="ch9.html#ex1">Ex1：统计未出现的类别</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch9.html#ex2">Ex2：钻石数据集</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="ch10.html">第十章 时序数据</a><ul>
<li class="toctree-l2"><a class="reference internal" href="ch10.html#id2">一、时序中的基本对象</a></li>
<li class="toctree-l2"><a class="reference internal" href="ch10.html#id3">二、时间戳</a><ul>
<li class="toctree-l3"><a class="reference internal" href="ch10.html#timestamp">1. Timestamp的构造与属性</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch10.html#datetime">2. Datetime序列的生成</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch10.html#dt">3. dt对象</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch10.html#id4">4. 时间戳的切片与索引</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="ch10.html#id5">三、时间差</a><ul>
<li class="toctree-l3"><a class="reference internal" href="ch10.html#timedelta">1. Timedelta的生成</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch10.html#id6">2. Timedelta的运算</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="ch10.html#id7">四、日期偏置</a><ul>
<li class="toctree-l3"><a class="reference internal" href="ch10.html#offset">1. Offset对象</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch10.html#id8">2. 偏置字符串</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="ch10.html#id9">五、时序中的滑窗与分组</a><ul>
<li class="toctree-l3"><a class="reference internal" href="ch10.html#id10">1. 滑动窗口</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch10.html#id11">2. 重采样</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="ch10.html#id12">六、练习</a><ul>
<li class="toctree-l3"><a class="reference internal" href="ch10.html#ex1">Ex1：太阳辐射数据集</a></li>
<li class="toctree-l3"><a class="reference internal" href="ch10.html#ex2">Ex2：水果销量数据集</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="%E5%8F%82%E8%80%83%E7%AD%94%E6%A1%88.html">参考答案</a><ul>
<li class="toctree-l2"><a class="reference internal" href="%E5%8F%82%E8%80%83%E7%AD%94%E6%A1%88.html#id2">第一章 预备知识</a><ul>
<li class="toctree-l3"><a class="reference internal" href="%E5%8F%82%E8%80%83%E7%AD%94%E6%A1%88.html#ex1">Ex1：利用列表推导式写矩阵乘法</a></li>
<li class="toctree-l3"><a class="reference internal" href="%E5%8F%82%E8%80%83%E7%AD%94%E6%A1%88.html#ex2">Ex2：更新矩阵</a></li>
<li class="toctree-l3"><a class="reference internal" href="%E5%8F%82%E8%80%83%E7%AD%94%E6%A1%88.html#ex3">Ex3：卡方统计量</a></li>
<li class="toctree-l3"><a class="reference internal" href="%E5%8F%82%E8%80%83%E7%AD%94%E6%A1%88.html#ex4">Ex4：改进矩阵计算的性能</a></li>
<li class="toctree-l3"><a class="reference internal" href="%E5%8F%82%E8%80%83%E7%AD%94%E6%A1%88.html#ex5">Ex5：连续整数的最大长度</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="%E5%8F%82%E8%80%83%E7%AD%94%E6%A1%88.html#pandas">第二章 pandas基础</a><ul>
<li class="toctree-l3"><a class="reference internal" href="%E5%8F%82%E8%80%83%E7%AD%94%E6%A1%88.html#id3">Ex1：口袋妖怪数据集</a></li>
<li class="toctree-l3"><a class="reference internal" href="%E5%8F%82%E8%80%83%E7%AD%94%E6%A1%88.html#id4">Ex2：指数加权窗口</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="%E5%8F%82%E8%80%83%E7%AD%94%E6%A1%88.html#id5">第三章 索引</a><ul>
<li class="toctree-l3"><a class="reference internal" href="%E5%8F%82%E8%80%83%E7%AD%94%E6%A1%88.html#id6">Ex1：公司员工数据集</a></li>
<li class="toctree-l3"><a class="reference internal" href="%E5%8F%82%E8%80%83%E7%AD%94%E6%A1%88.html#id7">Ex2：巧克力数据集</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="%E5%8F%82%E8%80%83%E7%AD%94%E6%A1%88.html#id8">第四章 分组</a><ul>
<li class="toctree-l3"><a class="reference internal" href="%E5%8F%82%E8%80%83%E7%AD%94%E6%A1%88.html#id9">Ex1：汽车数据集</a></li>
<li class="toctree-l3"><a class="reference internal" href="%E5%8F%82%E8%80%83%E7%AD%94%E6%A1%88.html#ex2-transform">Ex2：实现transform函数</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="%E5%8F%82%E8%80%83%E7%AD%94%E6%A1%88.html#id10">第五章 变形</a><ul>
<li class="toctree-l3"><a class="reference internal" href="%E5%8F%82%E8%80%83%E7%AD%94%E6%A1%88.html#id11">Ex1：美国非法药物数据集</a></li>
<li class="toctree-l3"><a class="reference internal" href="%E5%8F%82%E8%80%83%E7%AD%94%E6%A1%88.html#ex2-wide-to-long">Ex2：特殊的wide_to_long方法</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="%E5%8F%82%E8%80%83%E7%AD%94%E6%A1%88.html#id12">第六章 连接</a><ul>
<li class="toctree-l3"><a class="reference internal" href="%E5%8F%82%E8%80%83%E7%AD%94%E6%A1%88.html#id13">Ex1：美国疫情数据集</a></li>
<li class="toctree-l3"><a class="reference internal" href="%E5%8F%82%E8%80%83%E7%AD%94%E6%A1%88.html#ex2-join">Ex2：实现join函数</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="%E5%8F%82%E8%80%83%E7%AD%94%E6%A1%88.html#id14">第七章 缺失数据</a><ul>
<li class="toctree-l3"><a class="reference internal" href="%E5%8F%82%E8%80%83%E7%AD%94%E6%A1%88.html#id15">Ex1：缺失值与类别的相关性检验</a></li>
<li class="toctree-l3"><a class="reference internal" href="%E5%8F%82%E8%80%83%E7%AD%94%E6%A1%88.html#id16">Ex2：用回归模型解决分类问题</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="%E5%8F%82%E8%80%83%E7%AD%94%E6%A1%88.html#id17">第八章 文本数据</a><ul>
<li class="toctree-l3"><a class="reference internal" href="%E5%8F%82%E8%80%83%E7%AD%94%E6%A1%88.html#id18">Ex1：房屋信息数据集</a></li>
<li class="toctree-l3"><a class="reference internal" href="%E5%8F%82%E8%80%83%E7%AD%94%E6%A1%88.html#id19">Ex2：《权力的游戏》剧本数据集</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="%E5%8F%82%E8%80%83%E7%AD%94%E6%A1%88.html#id20">第九章 分类数据</a><ul>
<li class="toctree-l3"><a class="reference internal" href="%E5%8F%82%E8%80%83%E7%AD%94%E6%A1%88.html#id21">Ex1：统计未出现的类别</a></li>
<li class="toctree-l3"><a class="reference internal" href="%E5%8F%82%E8%80%83%E7%AD%94%E6%A1%88.html#id22">Ex2：钻石数据集</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="%E5%8F%82%E8%80%83%E7%AD%94%E6%A1%88.html#id23">第十章 时序数据</a><ul>
<li class="toctree-l3"><a class="reference internal" href="%E5%8F%82%E8%80%83%E7%AD%94%E6%A1%88.html#id24">Ex1：太阳辐射数据集</a></li>
<li class="toctree-l3"><a class="reference internal" href="%E5%8F%82%E8%80%83%E7%AD%94%E6%A1%88.html#id25">Ex2：水果销量数据集</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</section>


              </article>
              

              
          </div>
          
      </div>
    </div>

  
  
  <!-- Scripts loaded after <body> so the DOM is not blocked -->
  <script src="../_static/scripts/pydata-sphinx-theme.js?digest=92025949c220c2e29695"></script>

<footer class="bd-footer"><div class="bd-footer__inner container">
  
  <div class="footer-item">
    <p class="copyright">
    &copy; Copyright 2020-2022, Datawhale, 耿远昊.<br>
</p>
  </div>
  
  <div class="footer-item">
    <p class="sphinx-version">
Created using <a href="http://sphinx-doc.org/">Sphinx</a> 5.0.2.<br>
</p>
  </div>
  
</div>
</footer>
  </body>
</html>