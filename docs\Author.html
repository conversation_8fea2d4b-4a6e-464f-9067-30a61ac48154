
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

    <title>Author &#8212; Joyful Pandas 1.0 documentation</title>
<script>
  document.documentElement.dataset.mode = localStorage.getItem("mode") || "";
  document.documentElement.dataset.theme = localStorage.getItem("theme") || "light"
</script>

  <!-- Loaded before other Sphinx assets -->
  <link href="_static/styles/theme.css?digest=92025949c220c2e29695" rel="stylesheet">
<link href="_static/styles/pydata-sphinx-theme.css?digest=92025949c220c2e29695" rel="stylesheet">


  <link rel="stylesheet"
    href="_static/vendor/fontawesome/5.13.0/css/all.min.css">
  <link rel="preload" as="font" type="font/woff2" crossorigin
    href="_static/vendor/fontawesome/5.13.0/webfonts/fa-solid-900.woff2">
  <link rel="preload" as="font" type="font/woff2" crossorigin
    href="_static/vendor/fontawesome/5.13.0/webfonts/fa-brands-400.woff2">

    <link rel="stylesheet" type="text/css" href="_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="_static/plot_directive.css" />
    <link rel="stylesheet" type="text/css" href="_static/css/s4defs-roles.css" />

  <!-- Pre-loaded scripts that we'll load fully later -->
  <link rel="preload" as="script" href="_static/scripts/pydata-sphinx-theme.js?digest=92025949c220c2e29695">

    <script data-url_root="./" id="documentation_options" src="_static/documentation_options.js"></script>
    <script src="_static/jquery.js"></script>
    <script src="_static/underscore.js"></script>
    <script src="_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="_static/doctools.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="next" title="Datawhale" href="Datawhale.html" />
    <link rel="prev" title="参考答案" href="Content/%E5%8F%82%E8%80%83%E7%AD%94%E6%A1%88.html" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<meta name="docsearch:language" content="en">
  </head>
  
  
  <body data-spy="scroll" data-target="#bd-toc-nav" data-offset="180" data-default-mode="">
    <div class="bd-header-announcement container-fluid" id="banner">
      

    </div>

    
    <nav class="bd-header navbar navbar-light navbar-expand-lg bg-light fixed-top bd-navbar" id="navbar-main"><div class="bd-header__inner container-xl">

  <div id="navbar-start">
    
    
  


<a class="navbar-brand logo" href="index.html">
  
  
  
  
    <img src="_static/finallogo1.svg" class="logo__image only-light" alt="Logo image">
    <img src="_static/finallogo1.svg" class="logo__image only-dark" alt="Logo image">
  
  
</a>
    
  </div>

  <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbar-collapsible" aria-controls="navbar-collapsible" aria-expanded="false" aria-label="Toggle navigation">
    <span class="fas fa-bars"></span>
  </button>

  
  <div id="navbar-collapsible" class="col-lg-9 collapse navbar-collapse">
    <div id="navbar-center" class="mr-auto">
      
      <div class="navbar-center-item">
        <ul id="navbar-main-elements" class="navbar-nav">
    <li class="toctree-l1 nav-item">
 <a class="reference internal nav-link" href="Home.html">
  Home
 </a>
</li>

<li class="toctree-l1 nav-item">
 <a class="reference internal nav-link" href="Content/index.html">
  Content
 </a>
</li>

<li class="toctree-l1 current active nav-item">
 <a class="current reference internal nav-link" href="#">
  Author
 </a>
</li>

<li class="toctree-l1 nav-item">
 <a class="reference internal nav-link" href="Datawhale.html">
  Datawhale
 </a>
</li>

<li class="toctree-l1 nav-item">
 <a class="reference internal nav-link" href="pandas%E6%95%B0%E6%8D%AE%E5%A4%84%E7%90%86%E4%B8%8E%E5%88%86%E6%9E%90.html">
  pandas数据处理与分析
 </a>
</li>

<li class="toctree-l1 nav-item">
 <a class="reference internal nav-link" href="%E8%A1%A5%E5%85%85%E4%B9%A0%E9%A2%98.html">
  补充习题
 </a>
</li>

    
    <li class="nav-item">
        <a class="nav-link nav-external" href="https://pandas.pydata.org/docs/index.html">Doc<i class="fas fa-external-link-alt"></i></a>
    </li>
    
</ul>
      </div>
      
    </div>

    <div id="navbar-end">
      
      <div class="navbar-end-item">
        <span id="theme-switch" class="btn btn-sm btn-outline-primary navbar-btn rounded-circle">
    <a class="theme-switch" data-mode="light"><i class="fas fa-sun"></i></a>
    <a class="theme-switch" data-mode="dark"><i class="far fa-moon"></i></a>
    <a class="theme-switch" data-mode="auto"><i class="fas fa-adjust"></i></a>
</span>
      </div>
      
      <div class="navbar-end-item">
        <ul id="navbar-icon-links" class="navbar-nav" aria-label="Icon Links">
        <li class="nav-item">
          <a class="nav-link" href="https://github.com/datawhalechina/joyful-pandas" rel="noopener" target="_blank" title="GitHub"><span><i class="fab fa-github-square"></i></span>
            <label class="sr-only">GitHub</label></a>
        </li>
      </ul>
      </div>
      
    </div>
  </div>
</div>
    </nav>
    

    <div class="bd-container container-xl">
      <div class="bd-container__inner row">
          

<!-- Only show if we have sidebars configured, else just a small margin  -->
<div class="bd-sidebar-primary col-12 col-md-3 bd-sidebar">
  <div class="sidebar-start-items"><form class="bd-search d-flex align-items-center" action="search.html" method="get">
  <i class="icon fas fa-search"></i>
  <input type="search" class="form-control" name="q" id="search-input" placeholder="Search the docs ..." aria-label="Search the docs ..." autocomplete="off" >
</form><nav class="bd-links" id="bd-docs-nav" aria-label="Main navigation">
  <div class="bd-toc-item active">
    
  </div>
</nav>
  </div>
  <div class="sidebar-end-items">
  </div>
</div>


          


<div class="bd-sidebar-secondary d-none d-xl-block col-xl-2 bd-toc">
  
    
    <div class="toc-item">
      
<div class="tocsection onthispage mt-5 pt-1 pb-3">
    <i class="fas fa-list"></i> On this page
</div>

<nav id="bd-toc-nav">
    <ul class="visible nav section-nav flex-column">
 <li class="toc-h2 nav-item toc-entry">
  <a class="reference internal nav-link" href="#contributions-to-pandas-project">
   Contributions to pandas project
  </a>
  <ul class="nav section-nav flex-column">
   <li class="toc-h3 nav-item toc-entry">
    <a class="reference internal nav-link" href="#merged">
     Merged
    </a>
   </li>
  </ul>
 </li>
 <li class="toc-h2 nav-item toc-entry">
  <a class="reference internal nav-link" href="#mail">
   Mail
  </a>
 </li>
 <li class="toc-h2 nav-item toc-entry">
  <a class="reference internal nav-link" href="#github">
   Github
  </a>
 </li>
 <li class="toc-h2 nav-item toc-entry">
  <a class="reference internal nav-link" href="#wechat">
   WeChat
  </a>
 </li>
</ul>

</nav>
    </div>
    
    <div class="toc-item">
      
    </div>
    
  
</div>


          
          
          <div class="bd-content col-12 col-md-9 col-xl-7">
              
              <article class="bd-article" role="main">
                
  <section id="author">
<h1>Author<a class="headerlink" href="#author" title="Permalink to this heading">#</a></h1>
<p>耿远昊，华东师范大学统计学本科，威斯康星大学麦迪逊分校统计学硕士，Datawhale成员，《pandas数据处理与分析》作者。pandas contributor，积极参与pandas开源社区生态建设，包括漏洞修复、功能实现与性能优化等，对pandas在数据处理与分析中的应用有丰富经验。</p>
<section id="contributions-to-pandas-project">
<h2>Contributions to pandas project<a class="headerlink" href="#contributions-to-pandas-project" title="Permalink to this heading">#</a></h2>
<section id="merged">
<h3>Merged<a class="headerlink" href="#merged" title="Permalink to this heading">#</a></h3>
<ul class="simple">
<li><p><a class="reference external" href="https://github.com/pandas-dev/pandas/pull/33783">#33783</a> DOC: fix doc for <code class="docutils literal notranslate"><span class="pre">crosstab</span></code> with Categorical data input</p></li>
<li><p><a class="reference external" href="https://github.com/pandas-dev/pandas/pull/36516">#36516</a> DOC: Correct inconsistent description on default <code class="docutils literal notranslate"><span class="pre">DateOffset</span></code> setting</p></li>
<li><p><a class="reference external" href="https://github.com/pandas-dev/pandas/pull/37607">#37607</a> BUG: <code class="docutils literal notranslate"><span class="pre">nunique</span></code> not ignoring both <code class="docutils literal notranslate"><span class="pre">None</span></code> and <code class="docutils literal notranslate"><span class="pre">np.nan</span></code></p></li>
<li><p><a class="reference external" href="https://github.com/pandas-dev/pandas/pull/37830">#37830</a> BUG: <code class="docutils literal notranslate"><span class="pre">MultiIndex.drop</span></code> does not raise if labels are partially found</p></li>
<li><p><a class="reference external" href="https://github.com/pandas-dev/pandas/pull/38029">#38029</a> BUG: <code class="docutils literal notranslate"><span class="pre">unstack</span></code> with missing levels results in incorrect <code class="docutils literal notranslate"><span class="pre">index</span></code> names</p></li>
<li><p><a class="reference external" href="https://github.com/pandas-dev/pandas/pull/38089">#38089</a> BUG: <code class="docutils literal notranslate"><span class="pre">merge_ordered</span></code> fails with list-like <code class="docutils literal notranslate"><span class="pre">left_by</span></code> or <code class="docutils literal notranslate"><span class="pre">right_by</span></code></p></li>
<li><p><a class="reference external" href="https://github.com/pandas-dev/pandas/pull/38170">#38170</a> BUG: unexpected <code class="docutils literal notranslate"><span class="pre">merge_ordered</span></code> results caused by wrongly <code class="docutils literal notranslate"><span class="pre">groupby</span></code></p></li>
<li><p><a class="reference external" href="https://github.com/pandas-dev/pandas/pull/38173">#38173</a> BUG: array-like <code class="docutils literal notranslate"><span class="pre">quantile</span></code> fails on column <code class="docutils literal notranslate"><span class="pre">groupby</span></code></p></li>
<li><p><a class="reference external" href="https://github.com/pandas-dev/pandas/pull/38257">#38257</a> BUG: <code class="docutils literal notranslate"><span class="pre">groupby.apply</span></code> on the <code class="docutils literal notranslate"><span class="pre">NaN</span></code> group drops values with original <code class="docutils literal notranslate"><span class="pre">axes</span></code> return</p></li>
<li><p><a class="reference external" href="https://github.com/pandas-dev/pandas/pull/38408">#38408</a> ENH: add end and end_day <code class="docutils literal notranslate"><span class="pre">origin</span></code> for <code class="docutils literal notranslate"><span class="pre">resample</span></code></p></li>
<li><p><a class="reference external" href="https://github.com/pandas-dev/pandas/pull/38492">#38492</a> BUG: <code class="docutils literal notranslate"><span class="pre">CategoricalIndex.reindex</span></code> fails when <code class="docutils literal notranslate"><span class="pre">Index</span></code> passed with labels all in category</p></li>
<li><p><a class="reference external" href="https://github.com/pandas-dev/pandas/pull/44827">#44827</a> PERF: faster <code class="docutils literal notranslate"><span class="pre">Dataframe</span></code> construction from <code class="docutils literal notranslate"><span class="pre">recarray</span></code></p></li>
<li><p><a class="reference external" href="https://github.com/pandas-dev/pandas/pull/46546">#46546</a> BUG: <code class="docutils literal notranslate"><span class="pre">pd.concat</span></code> with identical key leads to multi-indexing error</p></li>
<li><p><a class="reference external" href="https://github.com/pandas-dev/pandas/pull/46654">#46654</a> TST: add validation checks on levels keyword from <code class="docutils literal notranslate"><span class="pre">pd.concat</span></code></p></li>
<li><p><a class="reference external" href="https://github.com/pandas-dev/pandas/pull/46656">#46656</a> BUG: <code class="docutils literal notranslate"><span class="pre">df.nsmallest</span></code> get wrong results when <code class="docutils literal notranslate"><span class="pre">NaN</span></code> in the sorting column</p></li>
<li><p><a class="reference external" href="https://github.com/pandas-dev/pandas/pull/47605">#47605</a> BUG: <code class="docutils literal notranslate"><span class="pre">df.groupby().resample()[[cols]]</span></code> without key columns raise <code class="docutils literal notranslate"><span class="pre">KeyError</span></code></p></li>
<li><p><a class="reference external" href="https://github.com/pandas-dev/pandas/pull/47685">#47685</a> TST: avoid sort when concat int-index <code class="docutils literal notranslate"><span class="pre">Dataframes</span></code> with <code class="docutils literal notranslate"><span class="pre">sort=False</span></code></p></li>
<li><p><a class="reference external" href="https://github.com/pandas-dev/pandas/pull/47708">#47708</a> BUG: <code class="docutils literal notranslate"><span class="pre">json_normalize</span></code> raises boardcasting error with list-like <code class="docutils literal notranslate"><span class="pre">metadata</span></code></p></li>
<li><p><a class="reference external" href="https://github.com/pandas-dev/pandas/pull/47714">#47714</a> BUG: <code class="docutils literal notranslate"><span class="pre">df.fillna</span></code> ignores axis when <code class="docutils literal notranslate"><span class="pre">DataFrame</span></code> is single block</p></li>
<li><p><a class="reference external" href="https://github.com/pandas-dev/pandas/pull/47717">#47717</a> TST: add test for <code class="docutils literal notranslate"><span class="pre">groupby</span></code> with <code class="docutils literal notranslate"><span class="pre">dropna=False</span></code> on multi-index</p></li>
<li><p><a class="reference external" href="https://github.com/pandas-dev/pandas/pull/47731">#47731</a> BUG: <code class="docutils literal notranslate"><span class="pre">groupby.corrwith</span></code> fails with <code class="docutils literal notranslate"><span class="pre">axis=1</span></code> and <code class="docutils literal notranslate"><span class="pre">other=df</span></code></p></li>
<li><p><a class="reference external" href="https://github.com/pandas-dev/pandas/pull/47757">#47757</a> BUG: <code class="docutils literal notranslate"><span class="pre">wide_to_long</span></code> fails when <code class="docutils literal notranslate"><span class="pre">stubnames</span></code> miss and <code class="docutils literal notranslate"><span class="pre">i</span></code> contains <code class="docutils literal notranslate"><span class="pre">string</span></code> column</p></li>
<li><p><a class="reference external" href="https://github.com/pandas-dev/pandas/pull/47779">#47779</a> PERF: efficient <code class="docutils literal notranslate"><span class="pre">argmax/argmin</span></code> for <code class="docutils literal notranslate"><span class="pre">SparseArray</span></code></p></li>
<li><p><a class="reference external" href="https://github.com/pandas-dev/pandas/pull/47810">#47810</a> BUG: fix <code class="docutils literal notranslate"><span class="pre">SparseArray.unique</span></code> IndexError and <code class="docutils literal notranslate"><span class="pre">_first_fill_value_loc</span></code> algo</p></li>
</ul>
</section>
</section>
<section id="mail">
<h2>Mail<a class="headerlink" href="#mail" title="Permalink to this heading">#</a></h2>
<p><a class="reference external" href="mailto:1801214626&#37;&#52;&#48;qq&#46;com">1801214626<span>&#64;</span>qq<span>&#46;</span>com</a></p>
</section>
<section id="github">
<h2>Github<a class="headerlink" href="#github" title="Permalink to this heading">#</a></h2>
<p><a class="reference external" href="https://github.com/GYHHAHA">https://github.com/GYHHAHA</a></p>
</section>
<section id="wechat">
<h2>WeChat<a class="headerlink" href="#wechat" title="Permalink to this heading">#</a></h2>
<a class="reference internal image-reference" href="_images/wx.png"><img alt="_images/wx.png" class="align-left" src="_images/wx.png" style="width: 200.0px; height: 200.0px;" /></a>
</section>
</section>


              </article>
              

              
          </div>
          
      </div>
    </div>

  
  
  <!-- Scripts loaded after <body> so the DOM is not blocked -->
  <script src="_static/scripts/pydata-sphinx-theme.js?digest=92025949c220c2e29695"></script>

<footer class="bd-footer"><div class="bd-footer__inner container">
  
  <div class="footer-item">
    <p class="copyright">
    &copy; Copyright 2020-2022, Datawhale, 耿远昊.<br>
</p>
  </div>
  
  <div class="footer-item">
    <p class="sphinx-version">
Created using <a href="http://sphinx-doc.org/">Sphinx</a> 5.0.2.<br>
</p>
  </div>
  
</div>
</footer>
  </body>
</html>