Home
=============

本教程共有十章，可分为三大模块：基础知识、四类操作、四类数据，涵盖了pandas的所有核心操作与特性。

在第一个模块中，包含了python基础、numpy基础和pandas基础三大部分。其中，python基础将回顾列表推导式、匿名函数、map对象以及zip对象的概念与应用；numpy基础包含了常见的数组操作，如：数组构造、数组变形与合并、数组切片、数组函数以及广播机制。pandas基础中涵盖了文件IO、数据存储结构、滑窗对象、以及pandas中的所有基本函数。

在第二个模块中，包含了索引、分组、变形、连接四类操作的介绍。其中，第三章索引涵盖了单层索引、多层索引、索引设定以及索引运算的内容；第四章分组介绍了分组对象的基本概念、聚合函数的使用、变换函数与过滤函数的用法，以及跨列分组的相关内容；第五章变形将讨论长宽表的变形、索引间的变形，以及类变形函数；第六章连接将涉及关系连接与方向连接的区别于使用方法，以及类变形函数的相关内容。

在第三个模块中，包含了缺失数据、文本数据、分类数据和时序数据的介绍。其中，第七章缺失数据将涉及其四大操作————汇总、删除、填充、插值，以及Nullable类型的详细介绍；第八章文本数据中将涵盖str对象，正则基础，文本的五大操作————拆分、合并、匹配、替换、提取，以及常用字符串函数；第九章分类数据将涉及cat对象、有序类别以及区间类别；第十章时序数据将涵盖时间戳对象、时间差对象、日期偏置、时序滑窗以及时序分组的内容。

每一个章节内部有三种特殊记号，分别是Warning、Note以及练一练，它们分别表示对于某个特性使用的警告、对于某个知识点的补充或者注释、针对上文所述内容的即时练习。此外，每个章节还配有相关的习题练习，以供读者巩固所学知识或引导读者进行深入的思考与总结。基于完整性，所有的章末练习作者都给出了参考答案。

本教程的pdf版本、notebook版本以及数据集及可至 `此处 <https://github.com/datawhalechina/joyful-pandas>`__ 下载。