************************************
pandas数据处理与分析
************************************

本书共有13章。前十章来自于Joyful Pandas教程，对正文、练一练和习题部分做了较多修订，在维持原有章节目录结构的前提下，各章在不同程度上对教程细节做出优化。在此基础上，本书新增了数据观测、特征工程以及性能优化的三个章节，数据观测能够帮读者较为全面地掌握可视化方法以及数据集观测的各类思路，特征工程一章阐述了各种特征构造以及特征选择的方法，这些内容能够在实际结构化数据的数据处理/科研/竞赛任务中被广泛运用，性能优化部分包含了我们应当如何编写高效的pandas代码以及如何运用多进程、Cython和Numba在最大程度上优化代码性能的相关内容。具体的练一练/习题变化以及新增章节情况可见 `GitHub仓库 <https://github.com/datawhalechina/joyful-pandas>`__ 。本书在写作期间，Joyful Pandas在 `pandas官网 <https://pandas.pydata.org/docs/dev/getting_started/tutorials.html#joyful-pandas>`__ 上被列为pandas的中文推荐教程，在此也对pandas核心开发组多年来的长期维护和社区建设表示感谢！

.. image:: _static/pandas封面.jpg
   :height: 300 px
   :align: right

购买链接
================

* `当当 <http://product.dangdang.com/29434656.html>`__
* `京东 <https://item.jd.com/13268767.html>`__

配套资源
================

* `数据集 <https://pan.baidu.com/s/16fgy9qYXo0JOsz3GIXQeKA>`__ （提取码：9e8r）
* `参考答案 <https://gyhhaha.github.io/pd-book/>`__

勘误
================

第1版第3次印刷
------------------------------------

- 作者简介，2022年12月毕业，硕士在读改为硕士
- 第40页，Out[78]结果的第一行或第二行删去，索引为0的行重复了
- 第62页，Out[53]第3行内层行索引的第一个元素应当为"Junior"
- 第133页，Out[32]输出应当为['nmm', 'nmm']
- 第144页，Out[99]输出的三个元素左侧都漏了两个星号
- 第200页，In[26]第一行应当为(8, 6)不是(8.6)
- 第209页，“图11.29中可视化的效果非常糟糕”，应为图11.32

第1版第2次印刷
------------------------------------

- 第58页，第1行末尾多了一个or，删去
- 第94页，In[18]倒数第2行的sep取值应当为"_"而不是空格
- 第101页，第7行公式，集合的竖线后面是a∈A，A漏了
- 第106页，Out[20]第2行A列对应值应当为1
- 第122页，第1行Series拼写错误
- 第135页，练一练8-2代码第一行读取文本应当为"r"而不是"w"
- 第196页，图11.11部分单元格与输出不匹配，使用如下版本：

.. image:: _static/11-11.png
   :scale: 60 %
   :align: center

第1版第1次印刷
------------------------------------

- 第15页，In[75]第1行应为a = np.array([[1, 2],[3, 4]])
- 第32页，In[35]第3行应为df["col_3"] = ["apple", "banana", "cat"]
- 第47页，习题3第（1）题第2段的第2行开头应为 “其中 :math:`w_0` 表示序列...”，删去“ :math:`=0` ”
- 第64页，In[67]应为df_ex.loc[idx["C":, ("D", "f"):]]
- 第70页，In[87]和Out[87]之间应存在分割代码块的空白
- 第98页，习题1的第1张表中First_Area一列应当所有元素全为字符“A”
- 第99页，习题2第3行改为“其中“日期”“统计类别”和“资源名称”3列已为...”
- 第104页，倒数第3行应当为“进行所有行的笛卡儿积”，不是“列”
- 第111页，文字段倒数第2行应当为“haversine_distances()”函数实现，漏了一个“s”
- 第115页，文字第2段第2行最后应当为“分别进行以上后2种情况的检索”
- 第119页，注解第3行最后应当为“而当选用spline的插值方法...”
- 第132页，Out[28]的Abc后面没有空格
- 第134页，In[41]第2行的banana前应当有一个空格
- 第185页，第二行应当为pip安装命令：pip install prophet
- 第210页，注解第1行应当为“pandas-profiling在3.1.x版本下...”
- 第223页，注解应当是“练一练”
- 第264页，In[14]倒数第2行应当缩进
- 第265页，In[16]的计时结果不算做Output，把Out[16]的记号删去
- 第265页，In[17]的计时结果应当和In[17]隔开
