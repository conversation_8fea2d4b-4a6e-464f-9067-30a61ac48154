import numpy as np
import pandas as pd
import warnings
warnings.filterwarnings('ignore')

# 显示pandas版本
print(f"pandas版本: {pd.__version__}")


# 题目1: 创建测试数据
test_data = {
    'Name': ['张三', '李四', '王五', '赵六'],
    'Age': [25, 30, 35, 28],
    'City': ['北京', '上海', '广州', '深圳'],
    'Salary': [8000, 12000, 15000, 9500]
}
df_test = pd.DataFrame(test_data)
print("测试数据:")
print(df_test)

# 将数据保存为CSV文件
df_test.to_csv('test_data.csv', index=False, encoding='utf-8')
print("\n数据已保存为 test_data.csv")


# 请完成以下任务:
# 1. 读取刚才保存的CSV文件
# 2. 只读取前2行数据
# 3. 只读取Name和Salary两列

# 任务1: 读取CSV文件
df_read = None  # 请补充代码

# 任务2: 读取前2行
df_head2 = None  # 请补充代码

# 任务3: 读取指定列
df_cols = None  # 请补充代码

print("读取结果验证:")
print("完整数据shape:", df_read.shape if df_read is not None else "未完成")
print("前2行shape:", df_head2.shape if df_head2 is not None else "未完成")
print("指定列shape:", df_cols.shape if df_cols is not None else "未完成")


# 任务: 创建Series和DataFrame
# 1. 创建一个包含混合数据类型的Series
data_mixed = [100, 'pandas', {'key': 'value'}]
index_mixed = ['数字', '字符串', '字典']

# 请创建Series，设置name为'混合数据'，index name为'数据类型'
s_mixed = None  # 请补充代码

# 2. 创建DataFrame并进行基本操作
product_data = {
    '产品名': ['笔记本', '鼠标', '键盘', '显示器'],
    '价格': [5000, 100, 300, 2000],
    '库存': [20, 50, 30, 15],
    '评分': [4.5, 4.0, 4.3, 4.8]
}

# 请创建DataFrame，行索引为['P001', 'P002', 'P003', 'P004']
df_product = None  # 请补充代码

# 3. 提取操作
# 提取'价格'列(返回Series)
price_series = None  # 请补充代码

# 提取'产品名'和'评分'列(返回DataFrame)
name_rating_df = None  # 请补充代码

print("验证结果:")
if s_mixed is not None:
    print(f"Series数据类型: {s_mixed.dtype}")
    print(f"Series名称: {s_mixed.name}")
    print(f"索引名称: {s_mixed.index.name}")
if df_product is not None:
    print(f"DataFrame形状: {df_product.shape}")
if price_series is not None:
    print(f"价格列类型: {type(price_series)}")
if name_rating_df is not None:
    print(f"双列提取形状: {name_rating_df.shape}")


# 创建学生成绩数据进行统计分析
np.random.seed(42)
students_data = pd.DataFrame({
    '班级': ['一班', '二班', '一班', '三班', '二班', '三班', '一班', '二班'],
    '姓名': ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十'],
    '数学': [85, 92, 78, 88, 95, 82, 90, 87],
    '英语': [88, 85, 92, 90, 80, 86, 89, 91]
})
print("学生成绩数据:")
print(students_data)

# 任务1: 计算数学和英语的平均分、最高分、75%分位数
math_mean = None     # 数学平均分
english_max = None   # 英语最高分  
math_q75 = None      # 数学75%分位数

# 任务2: 统计班级信息
class_unique = None     # 班级种类数
class_counts = None     # 各班级人数统计

# 任务3: 找出重复的班级-姓名组合(应该没有)
dup_check = None        # 检查是否有重复的班级-姓名组合

print("\n统计结果:")
print(f"数学平均分: {math_mean}")
print(f"英语最高分: {english_max}")
print(f"数学75%分位数: {math_q75}")
print(f"班级种类数: {class_unique}")
print(f"各班级人数: {class_counts}")
print(f"是否有重复班级-姓名组合: {dup_check.any() if dup_check is not None else '未完成'}")


# 创建需要进行替换操作的数据
replace_data = pd.DataFrame({
    '性别': ['男', '女', '男', '女', '男', '女'],
    '成绩等级': ['A', 'B', 'C', 'A', 'B', 'C'],
    '分数': [-5, 85, 150, 92, -10, 78],
    '是否及格': ['是', '是', '是', '是', '否', '是']
})
print("原始数据:")
print(replace_data)

# 任务1: 使用replace进行映射替换
# 将性别转换为数字编码：男->1, 女->0
gender_numeric = None  # 请补充代码

# 将成绩等级转换：A->优秀, B->良好, C->及格
grade_text = None  # 请补充代码

# 任务2: 使用where和mask进行条件替换
# 使用where将负分数替换为0
score_no_negative = None  # 请补充代码

# 使用mask将分数大于100的替换为100
score_capped = None  # 请补充代码

# 任务3: 使用数值替换函数
# 对分数取绝对值
score_abs = None  # 请补充代码

# 将分数限制在0-100范围内(使用clip)
score_clipped = None  # 请补充代码

print("\n替换结果验证:")
print(f"性别数字编码: {list(gender_numeric) if gender_numeric is not None else '未完成'}")
print(f"等级文本转换: {list(grade_text) if grade_text is not None else '未完成'}")
print(f"去除负分: {list(score_no_negative) if score_no_negative is not None else '未完成'}")
print(f"限制上限: {list(score_capped) if score_capped is not None else '未完成'}")
print(f"绝对值: {list(score_abs) if score_abs is not None else '未完成'}")
print(f"Clip限制: {list(score_clipped) if score_clipped is not None else '未完成'}")


# 创建需要排序的数据
sort_data = pd.DataFrame({
    '部门': ['销售部', '技术部', '销售部', '人事部', '技术部', '财务部'],
    '姓名': ['张三', '李四', '王五', '赵六', '钱七', '孙八'],
    '薪资': [8000, 12000, 7500, 6000, 13000, 9000],
    '工龄': [2, 5, 1, 3, 6, 4]
})
print("员工数据:")
print(sort_data)

# 任务1: 按薪资降序排列
salary_desc = None  # 请补充代码

# 任务2: 先按部门升序，再按薪资降序排列
multi_sort = None  # 请补充代码

# 任务3: 设置多级索引并进行索引排序
# 设置部门和姓名为多级索引
indexed_data = sort_data.set_index(['部门', '姓名'])
print("\n多级索引数据:")
print(indexed_data)

# 按索引排序(部门升序，姓名降序)
index_sorted = None  # 请补充代码

print("\n排序结果验证:")
if salary_desc is not None:
    print("按薪资降序(前3名):")
    print(salary_desc[['姓名', '薪资']].head(3))
    
if multi_sort is not None:
    print("\n多列排序(前3名):")
    print(multi_sort[['部门', '姓名', '薪资']].head(3))
    
if index_sorted is not None:
    print("\n索引排序(前3名):")
    print(index_sorted.head(3))


# 创建销售数据用于apply练习
sales_data = pd.DataFrame({
    '销售额': [10000, 15000, 8000, 20000, 12000],
    '成本': [6000, 9000, 5000, 12000, 7000], 
    '客户数': [100, 150, 80, 200, 120]
}, index=['Q1', 'Q2', 'Q3', 'Q4', 'Q5'])
print("季度销售数据:")
print(sales_data)

# 任务1: 使用apply计算列统计信息
# 计算每列的变异系数(标准差/均值)
def coefficient_variation(series):
    return series.std() / series.mean()

cv_result = None  # 请使用apply方法计算

# 任务2: 使用apply进行行计算
# 计算每个季度的利润率: (销售额-成本)/销售额
def profit_margin(row):
    return (row['销售额'] - row['成本']) / row['销售额']

profit_rate = None  # 请使用apply方法计算(注意axis参数)

# 任务3: 使用lambda表达式
# 计算每行数据与该行平均值的偏差
row_deviation = None  # 请使用apply和lambda计算

# 任务4: 自定义复杂函数
# 计算每列的"效率指标": (最大值-最小值)/平均值
def efficiency_index(series):
    return (series.max() - series.min()) / series.mean()

efficiency_result = None  # 请使用apply方法计算

print("\napply方法结果验证:")
print(f"变异系数:\\n{cv_result.round(3) if cv_result is not None else '未完成'}")
print(f"\\n利润率:\\n{profit_rate.round(3) if profit_rate is not None else '未完成'}")
if row_deviation is not None:
    print(f"\\n行偏差(前3季度):\\n{row_deviation.head(3).round(2)}")
else:
    print(f"\\n行偏差: 未完成")
print(f"\\n效率指标:\\n{efficiency_result.round(3) if efficiency_result is not None else '未完成'}")


# 创建时间序列数据用于窗口函数练习
dates = pd.date_range('2024-01-01', periods=10, freq='D')
daily_sales = pd.Series([100, 120, 90, 150, 110, 130, 95, 140, 125, 135], 
                       index=dates, name='日销售额')
print("日销售额数据:")
print(daily_sales)

# 任务1: 滑动窗口计算
# 计算3天移动平均
moving_avg_3 = None  # 请补充代码

# 计算5天移动最大值
moving_max_5 = None  # 请补充代码

# 任务2: 扩张窗口计算
# 计算累计平均销售额
cumulative_mean = None  # 请补充代码

# 计算累计最大销售额
cumulative_max = None  # 请补充代码

# 任务3: 类滑窗函数
# 计算昨日销售额(使用shift)
yesterday_sales = None  # 请补充代码

# 计算日增长额(使用diff)
daily_change = None  # 请补充代码

# 计算日增长率(使用pct_change)
daily_growth = None  # 请补充代码

# 任务4: 验证累计函数
# 使用expanding验证cumsum (先计算cumsum)
total_sales = daily_sales.cumsum()
expanding_sum = None  # 使用expanding().sum()验证

print("\\n窗口函数结果验证:")
print(f"3天移动平均(后5天):\\n{moving_avg_3.tail(5).round(2) if moving_avg_3 is not None else '未完成'}")
print(f"\\n5天移动最大值(后5天):\\n{moving_max_5.tail(5) if moving_max_5 is not None else '未完成'}")
print(f"\\n累计平均(后5天):\\n{cumulative_mean.tail(5).round(2) if cumulative_mean is not None else '未完成'}")
print(f"\\n昨日销售额(前5天):\\n{yesterday_sales.head(5) if yesterday_sales is not None else '未完成'}")
print(f"\\n日增长率(后5天):\\n{daily_growth.tail(5).round(3) if daily_growth is not None else '未完成'}")

# 验证expanding和cumsum结果是否一致
if expanding_sum is not None:
    is_equal = total_sales.equals(expanding_sum)
    print(f"\\nexpanding.sum()与cumsum()结果一致: {is_equal}")
else:
    print(f"\\nexpanding验证: 未完成")


# 创建综合测试数据
np.random.seed(100)
comprehensive_data = pd.DataFrame({
    '日期': pd.date_range('2024-01-01', periods=20, freq='D'),
    '产品类别': np.random.choice(['电子产品', '服装', '家居用品'], 20),
    '销售员': np.random.choice(['张三', '李四', '王五', '赵六'], 20),
    '销售额': np.random.randint(1000, 8000, 20),
    '成本': np.random.randint(500, 4000, 20),
    '客户评分': np.random.uniform(3.0, 5.0, 20).round(1),
    '是否促销': np.random.choice(['是', '否'], 20)
})

# 添加一些数据质量问题
comprehensive_data.loc[2, '销售额'] = -500  # 异常的负值
comprehensive_data.loc[5, '客户评分'] = 6.0   # 超出范围的评分
comprehensive_data.loc[10, '销售员'] = '张三'  # 创建重复组合用于测试

print("电商销售数据(前10行):")
print(comprehensive_data.head(10))
print(f"\\n数据形状: {comprehensive_data.shape}")


# ========== 综合分析任务 ==========
# 请使用本章学到的pandas方法完成以下分析任务
# 每完成一个任务，请添加相应的print输出验证结果

print("=== 综合分析任务 ===")
print("请在下方完成各项分析任务\\n")

# 任务1: 数据概览和基本信息 (5分)
print("【任务1: 数据概览】")
# 使用info()和describe()了解数据的基本情况
# 请在这里补充代码


# 任务2: 数据清洗 (8分)  
print("\\n【任务2: 数据清洗】")
# 2.1 将负的销售额替换为其绝对值
cleaned_sales = None  # 请补充代码

# 2.2 将超过5.0的客户评分限制为5.0
cleaned_rating = None  # 请补充代码

# 2.3 将促销状态转换为数字编码(是->1, 否->0)
promotion_encoded = None  # 请补充代码

print(f"清洗后销售额范围: {cleaned_sales.min() if cleaned_sales is not None else '未完成'} - {cleaned_sales.max() if cleaned_sales is not None else '未完成'}")
print(f"清洗后评分范围: {cleaned_rating.min() if cleaned_rating is not None else '未完成'} - {cleaned_rating.max() if cleaned_rating is not None else '未完成'}")
print(f"促销编码唯一值: {promotion_encoded.unique() if promotion_encoded is not None else '未完成'}")


# 任务3: 分组统计分析 (7分)
print("\\n【任务3: 分组统计分析】")
# 3.1 按产品类别统计销售额的平均值、最大值、最小值
category_stats = None  # 请使用统计函数

# 3.2 按销售员统计销售次数和总销售额
salesperson_stats = None  # 请使用value_counts和groupby相关方法

# 3.3 找出评分最高的销售记录
top_rating_record = None  # 请使用idxmax

print(f"产品类别统计: {category_stats.round(2) if category_stats is not None else '未完成'}")
print(f"评分最高记录索引: {top_rating_record if top_rating_record is not None else '未完成'}")


# 任务4: 排序和排名 (5分)
print("\\n【任务4: 排序和排名】")
# 4.1 按利润(销售额-成本)降序排列，显示前5名
profit_ranking = None  # 请先计算利润，再排序

# 4.2 按产品类别升序，销售额降序进行多列排序
multi_sort_result = None  # 请补充代码

print(f"利润前5名: {profit_ranking.head() if profit_ranking is not None else '未完成'}")


# 任务5: apply方法应用 (5分)
print("\\n【任务5: apply方法应用】")
# 5.1 计算每行的利润率 (利润/销售额)
profit_margin_apply = None  # 请使用apply方法

# 5.2 计算销售额、成本、评分三列的变异系数
variation_coeff = None  # 请使用apply方法

print(f"利润率统计: {profit_margin_apply.describe().round(3) if profit_margin_apply is not None else '未完成'}")
print(f"变异系数: {variation_coeff.round(3) if variation_coeff is not None else '未完成'}")

print("\\n=== 测验完成 ===")


# 清理测试文件
import os
files_to_remove = ['test_data.csv', 'test_data.xlsx', 'test_data.txt']
for file in files_to_remove:
    if os.path.exists(file):
        os.remove(file)
        print(f"已删除临时文件: {file}")

print("\\n🎉 第二章pandas基础测验完成！")
print("请检查您的答案，并根据评分标准进行自我评估。")
print("\\n📚 建议复习重点：")
print("1. 熟悉pandas基本数据结构的创建和操作")
print("2. 掌握各种统计函数和数据处理方法") 
print("3. 理解apply方法的使用场景和性能考虑")
print("4. 练习窗口函数在时间序列分析中的应用")
