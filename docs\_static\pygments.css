html[data-theme="light"] .highlight pre { line-height: 125%; }
html[data-theme="light"] .highlight td.linenos .normal { color: inherit; background-color: transparent; padding-left: 5px; padding-right: 5px; }
html[data-theme="light"] .highlight span.linenos { color: inherit; background-color: transparent; padding-left: 5px; padding-right: 5px; }
html[data-theme="light"] .highlight td.linenos .special { color: #000000; background-color: #ffffc0; padding-left: 5px; padding-right: 5px; }
html[data-theme="light"] .highlight span.linenos.special { color: #000000; background-color: #ffffc0; padding-left: 5px; padding-right: 5px; }
html[data-theme="light"] .highlight .hll { background-color: #ffffcc }
html[data-theme="light"] .highlight { background: #f8f8f8; }
html[data-theme="light"] .highlight .c { color: #8f5902; font-style: italic } /* Comment */
html[data-theme="light"] .highlight .err { color: #a40000; border: 1px solid #ef2929 } /* Error */
html[data-theme="light"] .highlight .g { color: #000000 } /* Generic */
html[data-theme="light"] .highlight .k { color: #204a87; font-weight: bold } /* Keyword */
html[data-theme="light"] .highlight .l { color: #000000 } /* Literal */
html[data-theme="light"] .highlight .n { color: #000000 } /* Name */
html[data-theme="light"] .highlight .o { color: #ce5c00; font-weight: bold } /* Operator */
html[data-theme="light"] .highlight .x { color: #000000 } /* Other */
html[data-theme="light"] .highlight .p { color: #000000; font-weight: bold } /* Punctuation */
html[data-theme="light"] .highlight .ch { color: #8f5902; font-style: italic } /* Comment.Hashbang */
html[data-theme="light"] .highlight .cm { color: #8f5902; font-style: italic } /* Comment.Multiline */
html[data-theme="light"] .highlight .cp { color: #8f5902; font-style: italic } /* Comment.Preproc */
html[data-theme="light"] .highlight .cpf { color: #8f5902; font-style: italic } /* Comment.PreprocFile */
html[data-theme="light"] .highlight .c1 { color: #8f5902; font-style: italic } /* Comment.Single */
html[data-theme="light"] .highlight .cs { color: #8f5902; font-style: italic } /* Comment.Special */
html[data-theme="light"] .highlight .gd { color: #a40000 } /* Generic.Deleted */
html[data-theme="light"] .highlight .ge { color: #000000; font-style: italic } /* Generic.Emph */
html[data-theme="light"] .highlight .gr { color: #ef2929 } /* Generic.Error */
html[data-theme="light"] .highlight .gh { color: #000080; font-weight: bold } /* Generic.Heading */
html[data-theme="light"] .highlight .gi { color: #00A000 } /* Generic.Inserted */
html[data-theme="light"] .highlight .go { color: #000000; font-style: italic } /* Generic.Output */
html[data-theme="light"] .highlight .gp { color: #8f5902 } /* Generic.Prompt */
html[data-theme="light"] .highlight .gs { color: #000000; font-weight: bold } /* Generic.Strong */
html[data-theme="light"] .highlight .gu { color: #800080; font-weight: bold } /* Generic.Subheading */
html[data-theme="light"] .highlight .gt { color: #a40000; font-weight: bold } /* Generic.Traceback */
html[data-theme="light"] .highlight .kc { color: #204a87; font-weight: bold } /* Keyword.Constant */
html[data-theme="light"] .highlight .kd { color: #204a87; font-weight: bold } /* Keyword.Declaration */
html[data-theme="light"] .highlight .kn { color: #204a87; font-weight: bold } /* Keyword.Namespace */
html[data-theme="light"] .highlight .kp { color: #204a87; font-weight: bold } /* Keyword.Pseudo */
html[data-theme="light"] .highlight .kr { color: #204a87; font-weight: bold } /* Keyword.Reserved */
html[data-theme="light"] .highlight .kt { color: #204a87; font-weight: bold } /* Keyword.Type */
html[data-theme="light"] .highlight .ld { color: #000000 } /* Literal.Date */
html[data-theme="light"] .highlight .m { color: #0000cf; font-weight: bold } /* Literal.Number */
html[data-theme="light"] .highlight .s { color: #4e9a06 } /* Literal.String */
html[data-theme="light"] .highlight .na { color: #c4a000 } /* Name.Attribute */
html[data-theme="light"] .highlight .nb { color: #204a87 } /* Name.Builtin */
html[data-theme="light"] .highlight .nc { color: #000000 } /* Name.Class */
html[data-theme="light"] .highlight .no { color: #000000 } /* Name.Constant */
html[data-theme="light"] .highlight .nd { color: #5c35cc; font-weight: bold } /* Name.Decorator */
html[data-theme="light"] .highlight .ni { color: #ce5c00 } /* Name.Entity */
html[data-theme="light"] .highlight .ne { color: #cc0000; font-weight: bold } /* Name.Exception */
html[data-theme="light"] .highlight .nf { color: #000000 } /* Name.Function */
html[data-theme="light"] .highlight .nl { color: #f57900 } /* Name.Label */
html[data-theme="light"] .highlight .nn { color: #000000 } /* Name.Namespace */
html[data-theme="light"] .highlight .nx { color: #000000 } /* Name.Other */
html[data-theme="light"] .highlight .py { color: #000000 } /* Name.Property */
html[data-theme="light"] .highlight .nt { color: #204a87; font-weight: bold } /* Name.Tag */
html[data-theme="light"] .highlight .nv { color: #000000 } /* Name.Variable */
html[data-theme="light"] .highlight .ow { color: #204a87; font-weight: bold } /* Operator.Word */
html[data-theme="light"] .highlight .pm { color: #000000; font-weight: bold } /* Punctuation.Marker */
html[data-theme="light"] .highlight .w { color: #f8f8f8 } /* Text.Whitespace */
html[data-theme="light"] .highlight .mb { color: #0000cf; font-weight: bold } /* Literal.Number.Bin */
html[data-theme="light"] .highlight .mf { color: #0000cf; font-weight: bold } /* Literal.Number.Float */
html[data-theme="light"] .highlight .mh { color: #0000cf; font-weight: bold } /* Literal.Number.Hex */
html[data-theme="light"] .highlight .mi { color: #0000cf; font-weight: bold } /* Literal.Number.Integer */
html[data-theme="light"] .highlight .mo { color: #0000cf; font-weight: bold } /* Literal.Number.Oct */
html[data-theme="light"] .highlight .sa { color: #4e9a06 } /* Literal.String.Affix */
html[data-theme="light"] .highlight .sb { color: #4e9a06 } /* Literal.String.Backtick */
html[data-theme="light"] .highlight .sc { color: #4e9a06 } /* Literal.String.Char */
html[data-theme="light"] .highlight .dl { color: #4e9a06 } /* Literal.String.Delimiter */
html[data-theme="light"] .highlight .sd { color: #8f5902; font-style: italic } /* Literal.String.Doc */
html[data-theme="light"] .highlight .s2 { color: #4e9a06 } /* Literal.String.Double */
html[data-theme="light"] .highlight .se { color: #4e9a06 } /* Literal.String.Escape */
html[data-theme="light"] .highlight .sh { color: #4e9a06 } /* Literal.String.Heredoc */
html[data-theme="light"] .highlight .si { color: #4e9a06 } /* Literal.String.Interpol */
html[data-theme="light"] .highlight .sx { color: #4e9a06 } /* Literal.String.Other */
html[data-theme="light"] .highlight .sr { color: #4e9a06 } /* Literal.String.Regex */
html[data-theme="light"] .highlight .s1 { color: #4e9a06 } /* Literal.String.Single */
html[data-theme="light"] .highlight .ss { color: #4e9a06 } /* Literal.String.Symbol */
html[data-theme="light"] .highlight .bp { color: #3465a4 } /* Name.Builtin.Pseudo */
html[data-theme="light"] .highlight .fm { color: #000000 } /* Name.Function.Magic */
html[data-theme="light"] .highlight .vc { color: #000000 } /* Name.Variable.Class */
html[data-theme="light"] .highlight .vg { color: #000000 } /* Name.Variable.Global */
html[data-theme="light"] .highlight .vi { color: #000000 } /* Name.Variable.Instance */
html[data-theme="light"] .highlight .vm { color: #000000 } /* Name.Variable.Magic */
html[data-theme="light"] .highlight .il { color: #0000cf; font-weight: bold } /* Literal.Number.Integer.Long */
html[data-theme="dark"] .highlight pre { line-height: 125%; }
html[data-theme="dark"] .highlight td.linenos .normal { color: #aaaaaa; background-color: transparent; padding-left: 5px; padding-right: 5px; }
html[data-theme="dark"] .highlight span.linenos { color: #aaaaaa; background-color: transparent; padding-left: 5px; padding-right: 5px; }
html[data-theme="dark"] .highlight td.linenos .special { color: #000000; background-color: #ffffc0; padding-left: 5px; padding-right: 5px; }
html[data-theme="dark"] .highlight span.linenos.special { color: #000000; background-color: #ffffc0; padding-left: 5px; padding-right: 5px; }
html[data-theme="dark"] .highlight .hll { background-color: #404040 }
html[data-theme="dark"] .highlight { background: #202020; color: #d0d0d0 }
html[data-theme="dark"] .highlight .c { color: #ababab; font-style: italic } /* Comment */
html[data-theme="dark"] .highlight .err { color: #a61717; background-color: #e3d2d2 } /* Error */
html[data-theme="dark"] .highlight .esc { color: #d0d0d0 } /* Escape */
html[data-theme="dark"] .highlight .g { color: #d0d0d0 } /* Generic */
html[data-theme="dark"] .highlight .k { color: #6ebf26; font-weight: bold } /* Keyword */
html[data-theme="dark"] .highlight .l { color: #d0d0d0 } /* Literal */
html[data-theme="dark"] .highlight .n { color: #d0d0d0 } /* Name */
html[data-theme="dark"] .highlight .o { color: #d0d0d0 } /* Operator */
html[data-theme="dark"] .highlight .x { color: #d0d0d0 } /* Other */
html[data-theme="dark"] .highlight .p { color: #d0d0d0 } /* Punctuation */
html[data-theme="dark"] .highlight .ch { color: #ababab; font-style: italic } /* Comment.Hashbang */
html[data-theme="dark"] .highlight .cm { color: #ababab; font-style: italic } /* Comment.Multiline */
html[data-theme="dark"] .highlight .cp { color: #cd2828; font-weight: bold } /* Comment.Preproc */
html[data-theme="dark"] .highlight .cpf { color: #ababab; font-style: italic } /* Comment.PreprocFile */
html[data-theme="dark"] .highlight .c1 { color: #ababab; font-style: italic } /* Comment.Single */
html[data-theme="dark"] .highlight .cs { color: #e50808; font-weight: bold; background-color: #520000 } /* Comment.Special */
html[data-theme="dark"] .highlight .gd { color: #d22323 } /* Generic.Deleted */
html[data-theme="dark"] .highlight .ge { color: #d0d0d0; font-style: italic } /* Generic.Emph */
html[data-theme="dark"] .highlight .gr { color: #d22323 } /* Generic.Error */
html[data-theme="dark"] .highlight .gh { color: #ffffff; font-weight: bold } /* Generic.Heading */
html[data-theme="dark"] .highlight .gi { color: #589819 } /* Generic.Inserted */
html[data-theme="dark"] .highlight .go { color: #cccccc } /* Generic.Output */
html[data-theme="dark"] .highlight .gp { color: #aaaaaa } /* Generic.Prompt */
html[data-theme="dark"] .highlight .gs { color: #d0d0d0; font-weight: bold } /* Generic.Strong */
html[data-theme="dark"] .highlight .gu { color: #ffffff; text-decoration: underline } /* Generic.Subheading */
html[data-theme="dark"] .highlight .gt { color: #d22323 } /* Generic.Traceback */
html[data-theme="dark"] .highlight .kc { color: #6ebf26; font-weight: bold } /* Keyword.Constant */
html[data-theme="dark"] .highlight .kd { color: #6ebf26; font-weight: bold } /* Keyword.Declaration */
html[data-theme="dark"] .highlight .kn { color: #6ebf26; font-weight: bold } /* Keyword.Namespace */
html[data-theme="dark"] .highlight .kp { color: #6ebf26 } /* Keyword.Pseudo */
html[data-theme="dark"] .highlight .kr { color: #6ebf26; font-weight: bold } /* Keyword.Reserved */
html[data-theme="dark"] .highlight .kt { color: #6ebf26; font-weight: bold } /* Keyword.Type */
html[data-theme="dark"] .highlight .ld { color: #d0d0d0 } /* Literal.Date */
html[data-theme="dark"] .highlight .m { color: #51b2fd } /* Literal.Number */
html[data-theme="dark"] .highlight .s { color: #ed9d13 } /* Literal.String */
html[data-theme="dark"] .highlight .na { color: #bbbbbb } /* Name.Attribute */
html[data-theme="dark"] .highlight .nb { color: #2fbccd } /* Name.Builtin */
html[data-theme="dark"] .highlight .nc { color: #71adff; text-decoration: underline } /* Name.Class */
html[data-theme="dark"] .highlight .no { color: #40ffff } /* Name.Constant */
html[data-theme="dark"] .highlight .nd { color: #ffa500 } /* Name.Decorator */
html[data-theme="dark"] .highlight .ni { color: #d0d0d0 } /* Name.Entity */
html[data-theme="dark"] .highlight .ne { color: #bbbbbb } /* Name.Exception */
html[data-theme="dark"] .highlight .nf { color: #71adff } /* Name.Function */
html[data-theme="dark"] .highlight .nl { color: #d0d0d0 } /* Name.Label */
html[data-theme="dark"] .highlight .nn { color: #71adff; text-decoration: underline } /* Name.Namespace */
html[data-theme="dark"] .highlight .nx { color: #d0d0d0 } /* Name.Other */
html[data-theme="dark"] .highlight .py { color: #d0d0d0 } /* Name.Property */
html[data-theme="dark"] .highlight .nt { color: #6ebf26; font-weight: bold } /* Name.Tag */
html[data-theme="dark"] .highlight .nv { color: #40ffff } /* Name.Variable */
html[data-theme="dark"] .highlight .ow { color: #6ebf26; font-weight: bold } /* Operator.Word */
html[data-theme="dark"] .highlight .pm { color: #d0d0d0 } /* Punctuation.Marker */
html[data-theme="dark"] .highlight .w { color: #666666 } /* Text.Whitespace */
html[data-theme="dark"] .highlight .mb { color: #51b2fd } /* Literal.Number.Bin */
html[data-theme="dark"] .highlight .mf { color: #51b2fd } /* Literal.Number.Float */
html[data-theme="dark"] .highlight .mh { color: #51b2fd } /* Literal.Number.Hex */
html[data-theme="dark"] .highlight .mi { color: #51b2fd } /* Literal.Number.Integer */
html[data-theme="dark"] .highlight .mo { color: #51b2fd } /* Literal.Number.Oct */
html[data-theme="dark"] .highlight .sa { color: #ed9d13 } /* Literal.String.Affix */
html[data-theme="dark"] .highlight .sb { color: #ed9d13 } /* Literal.String.Backtick */
html[data-theme="dark"] .highlight .sc { color: #ed9d13 } /* Literal.String.Char */
html[data-theme="dark"] .highlight .dl { color: #ed9d13 } /* Literal.String.Delimiter */
html[data-theme="dark"] .highlight .sd { color: #ed9d13 } /* Literal.String.Doc */
html[data-theme="dark"] .highlight .s2 { color: #ed9d13 } /* Literal.String.Double */
html[data-theme="dark"] .highlight .se { color: #ed9d13 } /* Literal.String.Escape */
html[data-theme="dark"] .highlight .sh { color: #ed9d13 } /* Literal.String.Heredoc */
html[data-theme="dark"] .highlight .si { color: #ed9d13 } /* Literal.String.Interpol */
html[data-theme="dark"] .highlight .sx { color: #ffa500 } /* Literal.String.Other */
html[data-theme="dark"] .highlight .sr { color: #ed9d13 } /* Literal.String.Regex */
html[data-theme="dark"] .highlight .s1 { color: #ed9d13 } /* Literal.String.Single */
html[data-theme="dark"] .highlight .ss { color: #ed9d13 } /* Literal.String.Symbol */
html[data-theme="dark"] .highlight .bp { color: #2fbccd } /* Name.Builtin.Pseudo */
html[data-theme="dark"] .highlight .fm { color: #71adff } /* Name.Function.Magic */
html[data-theme="dark"] .highlight .vc { color: #40ffff } /* Name.Variable.Class */
html[data-theme="dark"] .highlight .vg { color: #40ffff } /* Name.Variable.Global */
html[data-theme="dark"] .highlight .vi { color: #40ffff } /* Name.Variable.Instance */
html[data-theme="dark"] .highlight .vm { color: #40ffff } /* Name.Variable.Magic */
html[data-theme="dark"] .highlight .il { color: #51b2fd } /* Literal.Number.Integer.Long */